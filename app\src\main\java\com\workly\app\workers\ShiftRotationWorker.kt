package com.workly.app.workers

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.*
import com.workly.app.services.ShiftRotationService
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import java.util.concurrent.TimeUnit

/**
 * Worker chạy nền để kiểm tra và thực hiện xoay ca tự động
 */
@HiltWorker
class ShiftRotationWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val shiftRotationService: ShiftRotationService
) : CoroutineWorker(context, workerParams) {

    override suspend fun doWork(): Result {
        return try {
            // Kiểm tra và thực hiện xoay ca nếu cần
            val result = shiftRotationService.checkAndRotateShift()
            
            if (result.success) {
                // Xoay ca thành công
                Result.success(
                    workDataOf(
                        "rotation_success" to true,
                        "previous_shift" to result.previousShiftId,
                        "new_shift" to result.newShiftId,
                        "message" to result.message
                    )
                )
            } else {
                // Không cần xoay ca hoặc có lỗi nhỏ
                Result.success(
                    workDataOf(
                        "rotation_success" to false,
                        "message" to result.message,
                        "errors" to result.errors.joinToString(", ")
                    )
                )
            }
            
        } catch (e: Exception) {
            // Lỗi nghiêm trọng, thử lại
            Result.retry()
        }
    }

    companion object {
        const val WORK_NAME = "shift_rotation_check"
        
        /**
         * Lên lịch kiểm tra xoay ca định kỳ
         */
        fun schedulePeriodicCheck(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .setRequiresBatteryNotLow(false)
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false)
                .setRequiresStorageNotLow(false)
                .build()

            val periodicWorkRequest = PeriodicWorkRequestBuilder<ShiftRotationWorker>(
                repeatInterval = 1, // Kiểm tra mỗi ngày
                repeatIntervalTimeUnit = TimeUnit.DAYS,
                flexTimeInterval = 2, // Linh hoạt trong vòng 2 giờ
                flexTimeIntervalUnit = TimeUnit.HOURS
            )
                .setConstraints(constraints)
                .setInitialDelay(1, TimeUnit.HOURS) // Delay 1 giờ sau khi schedule
                .addTag("shift_rotation")
                .build()

            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    WORK_NAME,
                    ExistingPeriodicWorkPolicy.KEEP, // Giữ work hiện tại nếu đã có
                    periodicWorkRequest
                )
        }
        
        /**
         * Hủy kiểm tra xoay ca định kỳ
         */
        fun cancelPeriodicCheck(context: Context) {
            WorkManager.getInstance(context)
                .cancelUniqueWork(WORK_NAME)
        }
        
        /**
         * Chạy kiểm tra xoay ca ngay lập tức
         */
        fun runImmediateCheck(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .build()

            val immediateWorkRequest = OneTimeWorkRequestBuilder<ShiftRotationWorker>()
                .setConstraints(constraints)
                .addTag("shift_rotation_immediate")
                .build()

            WorkManager.getInstance(context)
                .enqueueUniqueWork(
                    "shift_rotation_immediate",
                    ExistingWorkPolicy.REPLACE,
                    immediateWorkRequest
                )
        }
    }
}

/**
 * Worker để gửi nhắc nhở xoay ca trước khi đến hạn
 */
@HiltWorker
class ShiftRotationReminderWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val shiftRotationService: ShiftRotationService
) : CoroutineWorker(context, workerParams) {

    override suspend fun doWork(): Result {
        return try {
            val rotationInfo = shiftRotationService.getCurrentRotationInfo()
            
            if (rotationInfo != null && rotationInfo.daysUntilNextRotation <= 1) {
                // Gửi nhắc nhở nếu còn 1 ngày hoặc ít hơn
                rotationInfo.nextShift?.let { nextShift ->
                    rotationInfo.nextRotationDate?.let { rotationDate ->
                        shiftRotationService.sendRotationReminder(nextShift, rotationDate)
                    }
                }
            }
            
            Result.success()
            
        } catch (e: Exception) {
            Result.failure()
        }
    }

    companion object {
        const val WORK_NAME = "shift_rotation_reminder"
        
        /**
         * Lên lịch nhắc nhở xoay ca
         */
        fun scheduleReminder(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .build()

            val periodicWorkRequest = PeriodicWorkRequestBuilder<ShiftRotationReminderWorker>(
                repeatInterval = 1,
                repeatIntervalTimeUnit = TimeUnit.DAYS
            )
                .setConstraints(constraints)
                .setInitialDelay(12, TimeUnit.HOURS) // Chạy vào buổi trưa
                .addTag("shift_rotation_reminder")
                .build()

            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    WORK_NAME,
                    ExistingPeriodicWorkPolicy.KEEP,
                    periodicWorkRequest
                )
        }
        
        /**
         * Hủy nhắc nhở xoay ca
         */
        fun cancelReminder(context: Context) {
            WorkManager.getInstance(context)
                .cancelUniqueWork(WORK_NAME)
        }
    }
}

/**
 * Utility class để quản lý tất cả các worker liên quan đến xoay ca
 */
object ShiftRotationWorkManager {
    
    /**
     * Khởi tạo tất cả worker liên quan đến xoay ca
     */
    fun initialize(context: Context) {
        ShiftRotationWorker.schedulePeriodicCheck(context)
        ShiftRotationReminderWorker.scheduleReminder(context)
    }
    
    /**
     * Dừng tất cả worker liên quan đến xoay ca
     */
    fun shutdown(context: Context) {
        ShiftRotationWorker.cancelPeriodicCheck(context)
        ShiftRotationReminderWorker.cancelReminder(context)
    }
    
    /**
     * Khởi động lại tất cả worker (khi thay đổi cấu hình)
     */
    fun restart(context: Context) {
        shutdown(context)
        initialize(context)
    }
    
    /**
     * Kiểm tra trạng thái worker
     */
    fun getWorkStatus(context: Context): Map<String, WorkInfo.State> {
        val workManager = WorkManager.getInstance(context)
        val statuses = mutableMapOf<String, WorkInfo.State>()
        
        try {
            val rotationWork = workManager.getWorkInfosForUniqueWork(
                ShiftRotationWorker.WORK_NAME
            ).get()
            statuses["rotation_check"] = rotationWork.firstOrNull()?.state 
                ?: WorkInfo.State.CANCELLED
            
            val reminderWork = workManager.getWorkInfosForUniqueWork(
                ShiftRotationReminderWorker.WORK_NAME
            ).get()
            statuses["rotation_reminder"] = reminderWork.firstOrNull()?.state 
                ?: WorkInfo.State.CANCELLED
                
        } catch (e: Exception) {
            // Handle error
        }
        
        return statuses
    }
}

@echo off
echo ========================================
echo    Checking All KSP Fixes - Workly Android
echo ========================================
echo.

echo [1/4] Checking AttendanceLog.kt imports...
findstr /n "import.*LocalDate" "app\src\main\java\com\workly\app\data\model\AttendanceLog.kt" >nul
if %errorlevel%==0 (
    echo ✓ LocalDate import found in AttendanceLog.kt
) else (
    echo ✗ LocalDate import missing in AttendanceLog.kt
)

echo.
echo [2/4] Checking DailyWorkStatusDao.kt imports...
findstr /n "import.*ComplianceStatus" "app\src\main\java\com\workly\app\data\database\dao\DailyWorkStatusDao.kt" >nul
if %errorlevel%==0 (
    echo ✓ ComplianceStatus import found in DailyWorkStatusDao.kt
) else (
    echo ✗ ComplianceStatus import missing in DailyWorkStatusDao.kt
)

findstr /n "WorkStatus" "app\src\main\java\com\workly\app\data\database\dao\DailyWorkStatusDao.kt" >nul
if %errorlevel%==0 (
    echo ✗ WorkStatus still referenced in DailyWorkStatusDao.kt
) else (
    echo ✓ No WorkStatus references in DailyWorkStatusDao.kt
)

echo.
echo [3/4] Checking query column names...
findstr /n "complianceStatus" "app\src\main\java\com\workly\app\data\database\dao\DailyWorkStatusDao.kt" >nul
if %errorlevel%==0 (
    echo ✓ Queries use correct column name 'complianceStatus'
) else (
    echo ✗ Queries may use incorrect column names
)

echo.
echo [4/4] KSP Fixes Summary:
echo ✅ Fixed missing LocalDate import in AttendanceLog.kt
echo ✅ Replaced WorkStatus with ComplianceStatus in DailyWorkStatusDao.kt
echo ✅ Updated SQL queries to use correct column names
echo ✅ All Room entity classes have proper type references

echo.
echo All KSP MissingType errors should now be resolved!
echo The project should build successfully with Room and Hilt.

pause

package com.workly.app.data.repository

import com.workly.app.data.model.*
import com.workly.app.services.WorkCalculationService
import kotlinx.coroutines.flow.first
import java.time.LocalDate
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class WorkCalculationRepositoryImpl @Inject constructor(
    private val workCalculationService: WorkCalculationService,
    private val attendanceRepository: AttendanceRepository,
    private val shiftRepository: ShiftRepository,
    private val dailyWorkStatusRepository: DailyWorkStatusRepository,
    private val settingsRepository: SettingsRepository
) : WorkCalculationRepository {

    override suspend fun calculateAndUpdateDailyWork(
        date: LocalDate,
        shiftId: String,
        forceRecalculate: Boolean
    ): WorkCalculation? {
        try {
            // Kiểm tra xem đã tính toán chưa (nếu không force)
            if (!forceRecalculate) {
                val existingStatus = dailyWorkStatusRepository.getDailyWorkStatusByDate(date)
                if (existingStatus != null &&
                    existingStatus.calculationVersion >= 1 &&
                    existingStatus.appliedShiftIdForDay == shiftId) {
                    // Đã tính toán với version hiện tại, không cần tính lại
                    return convertToWorkCalculation(existingStatus)
                }
            }
            
            // Lấy thông tin ca làm việc
            val shift = shiftRepository.getShiftById(shiftId) ?: return null
            
            // Lấy logs chấm công cho ngày này
            val attendanceLogs = attendanceRepository.getLogsForDate(date)
            
            // Lấy cấu hình tính toán
            val config = getCalculationConfig()
            
            // Thực hiện tính toán
            val calculation = workCalculationService.calculateDailyWork(
                date = date,
                shift = shift,
                attendanceLogs = attendanceLogs,
                config = config
            )
            
            // Cập nhật DailyWorkStatus
            updateDailyWorkStatus(calculation)
            
            return calculation
            
        } catch (e: Exception) {
            // Log error
            return null
        }
    }

    override suspend fun getWorkCalculation(date: LocalDate): WorkCalculation? {
        val status = dailyWorkStatusRepository.getDailyWorkStatusByDate(date)
        return status?.let { convertToWorkCalculation(it) }
    }

    override suspend fun getWorkCalculations(
        fromDate: LocalDate,
        toDate: LocalDate
    ): List<WorkCalculation> {
        val statusList = dailyWorkStatusRepository.getStatusForDateRange(fromDate, toDate)
        return statusList.mapNotNull { convertToWorkCalculation(it) }
    }

    override suspend fun getWorkSummary(
        fromDate: LocalDate,
        toDate: LocalDate
    ): WorkSummary {
        val calculations = getWorkCalculations(fromDate, toDate)
        return workCalculationService.calculateWorkSummary(fromDate, toDate, calculations)
    }

    override suspend fun updateDailyWorkStatus(calculation: WorkCalculation) {
        val status = workCalculationService.updateDailyWorkStatus(calculation.date, calculation)
        dailyWorkStatusRepository.insertDailyWorkStatus(status)
    }

    override suspend fun recalculateWorkData(
        fromDate: LocalDate,
        toDate: LocalDate
    ): List<WorkCalculation> {
        val calculations = mutableListOf<WorkCalculation>()
        val settings = settingsRepository.getUserSettings().first()
        val activeShiftId = settings.activeShiftId
        
        if (activeShiftId != null) {
            var currentDate = fromDate
            while (!currentDate.isAfter(toDate)) {
                calculateAndUpdateDailyWork(
                    date = currentDate,
                    shiftId = activeShiftId,
                    forceRecalculate = true
                )?.let { calculations.add(it) }
                
                currentDate = currentDate.plusDays(1)
            }
        }
        
        return calculations
    }

    override suspend fun getCalculationConfig(): WorkCalculationConfig {
        val settings = settingsRepository.getUserSettings().first()
        
        return WorkCalculationConfig(
            lateThresholdMinutes = settings.lateThresholdMinutes,
            earlyThresholdMinutes = settings.earlyThresholdMinutes,
            overtimeThresholdMinutes = 30, // Default
            roundingMinutes = settings.roundingMinutes,
            overtimeMultiplier = settings.overtimeRates.weekday,
            nightShiftMultiplier = settings.nightShiftRates.multiplier,
            holidayMultiplier = settings.holidayRates.multiplier,
            nightShiftStartHour = settings.nightShiftRates.startHour,
            nightShiftEndHour = settings.nightShiftRates.endHour,
            nightShiftMinimumHours = settings.nightShiftRates.minimumHours,
            defaultBreakMinutes = 60, // Default
            maxUnpaidBreakMinutes = 90, // Default
            autoDeductBreak = true,
            maxOvertimeHoursPerDay = 4.0,
            maxOvertimeHoursPerWeek = 20.0,
            requireOvertimeApproval = false
        )
    }

    override suspend fun updateCalculationConfig(config: WorkCalculationConfig) {
        // Update user settings based on config
        val currentSettings = settingsRepository.getUserSettings().first()
        val updatedSettings = currentSettings.copy(
            lateThresholdMinutes = config.lateThresholdMinutes,
            earlyThresholdMinutes = config.earlyThresholdMinutes,
            roundingMinutes = config.roundingMinutes,
            overtimeRates = currentSettings.overtimeRates.copy(
                weekday = config.overtimeMultiplier
            ),
            nightShiftRates = currentSettings.nightShiftRates.copy(
                multiplier = config.nightShiftMultiplier,
                startHour = config.nightShiftStartHour,
                endHour = config.nightShiftEndHour,
                minimumHours = config.nightShiftMinimumHours
            ),
            holidayRates = currentSettings.holidayRates.copy(
                multiplier = config.holidayMultiplier
            )
        )
        
        settingsRepository.updateUserSettings(updatedSettings)
    }

    override suspend fun generateWorkReport(
        fromDate: LocalDate,
        toDate: LocalDate
    ): WorkSummary {
        val calculations = getWorkCalculations(fromDate, toDate)
        // Use the service to generate WorkSummary directly
        return workCalculationService.calculateWorkSummary(fromDate, toDate, calculations)
    }

    override suspend fun getComplianceStatistics(
        fromDate: LocalDate,
        toDate: LocalDate
    ): ComplianceStatistics {
        val calculations = getWorkCalculations(fromDate, toDate)
        
        val totalWorkDays = calculations.size
        val perfectComplianceDays = calculations.count { 
            it.complianceStatus == ComplianceStatus.PERFECT_COMPLIANCE 
        }
        val lateDays = calculations.count { it.lateMinutes > 0 }
        val earlyDays = calculations.count { it.earlyLeaveMinutes > 0 }
        val missingLogDays = calculations.count { 
            it.complianceStatus == ComplianceStatus.MISSING_BOTH ||
            it.complianceStatus == ComplianceStatus.MISSING_CHECK_IN ||
            it.complianceStatus == ComplianceStatus.MISSING_CHECK_OUT
        }
        
        val averageLateMinutes = if (lateDays > 0) {
            calculations.filter { it.lateMinutes > 0 }.map { it.lateMinutes }.average()
        } else 0.0
        
        val averageEarlyMinutes = if (earlyDays > 0) {
            calculations.filter { it.earlyLeaveMinutes > 0 }.map { it.earlyLeaveMinutes }.average()
        } else 0.0
        
        val complianceRate = if (totalWorkDays > 0) {
            (perfectComplianceDays.toDouble() / totalWorkDays) * 100
        } else 0.0
        
        val punctualityScore = workCalculationService.calculateComplianceScore(calculations)
        
        val trendData = calculations.map { calc ->
            ComplianceTrendPoint(
                date = calc.date,
                score = calc.getEfficiencyScore(),
                status = calc.complianceStatus,
                lateMinutes = calc.lateMinutes,
                earlyMinutes = calc.earlyLeaveMinutes
            )
        }
        
        return ComplianceStatistics(
            totalWorkDays = totalWorkDays,
            perfectComplianceDays = perfectComplianceDays,
            lateDays = lateDays,
            earlyDays = earlyDays,
            missingLogDays = missingLogDays,
            averageLateMinutes = averageLateMinutes,
            averageEarlyMinutes = averageEarlyMinutes,
            complianceRate = complianceRate,
            punctualityScore = punctualityScore,
            trendData = trendData,
            topIssues = emptyList() // Would be populated with analysis
        )
    }

    override suspend fun onAttendanceLogChanged(date: LocalDate, attendanceLog: AttendanceLog) {
        // Tự động tính lại khi có thay đổi log chấm công
        val settings = settingsRepository.getUserSettings().first()
        settings.activeShiftId?.let { shiftId ->
            calculateAndUpdateDailyWork(
                date = date,
                shiftId = shiftId,
                forceRecalculate = true
            )
        }
    }

    override suspend fun onShiftChanged(shiftId: String, affectedDates: List<LocalDate>) {
        // Tính lại cho tất cả ngày bị ảnh hưởng
        affectedDates.forEach { date ->
            calculateAndUpdateDailyWork(
                date = date,
                shiftId = shiftId,
                forceRecalculate = true
            )
        }
    }
    
    private fun convertToWorkCalculation(status: DailyWorkStatus): WorkCalculation? {
        // Convert DailyWorkStatus back to WorkCalculation
        // This is a simplified conversion - in practice, you might want to store
        // WorkCalculation separately or reconstruct it from available data
        
        return WorkCalculation(
            date = status.date,
            shiftId = status.appliedShiftIdForDay ?: "",
            scheduledStartTime = status.actualCheckInTime ?: status.date.atTime(8, 0),
            scheduledEndTime = status.actualCheckOutTime ?: status.date.atTime(17, 0),
            scheduledBreakMinutes = 60, // Default
            scheduledWorkMinutes = (status.totalHoursScheduled * 60).toInt(),
            actualDepartureTime = status.actualDepartureTime,
            actualCheckInTime = status.actualCheckInTime,
            actualCheckOutTime = status.actualCheckOutTime,
            actualArrivalTime = status.actualArrivalTime,
            complianceStatus = status.complianceStatus,
            lateMinutes = status.lateMinutes,
            earlyLeaveMinutes = status.earlyLeaveMinutes,
            totalDeviationMinutes = status.lateMinutes + status.earlyLeaveMinutes,
            standardHours = status.standardHoursScheduled,
            overtimeHours = status.otHoursScheduled,
            nightShiftHours = status.nightHoursScheduled,
            holidayHours = status.holidayHoursScheduled,
            sundayHours = status.sundayHoursScheduled,
            totalPaidHours = status.totalHoursScheduled,
            isNightShift = status.isNightShiftDay,
            isHoliday = status.isHolidayWork,
            isSunday = status.date.dayOfWeek.value == 7,
            hasBreakTime = true,
            actualBreakMinutes = 60, // Default
            calculatedAt = status.lastCalculatedAt,
            calculationVersion = status.calculationVersion
        )
    }
}

package com.workly.app.services

import com.workly.app.data.model.*
import com.workly.app.data.repository.SettingsRepository
import kotlinx.coroutines.flow.first
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.temporal.ChronoUnit
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.max

@Singleton
class WorkCalculationServiceImpl @Inject constructor(
    private val settingsRepository: SettingsRepository
) : WorkCalculationService {

    override suspend fun calculateDailyWork(
        date: LocalDate,
        shift: Shift,
        attendanceLogs: List<AttendanceLog>,
        config: WorkCalculationConfig
    ): WorkCalculation {
        
        // Lọc logs cho ngày cụ thể
        val dayLogs = attendanceLogs.filter { it.workDate == date }
        
        // Xác định thời gian theo lịch trình
        val scheduledStart = date.atTime(LocalTime.parse(shift.startTime))
        val scheduledEnd = if (shift.isNightShift && shift.endTime < shift.startTime) {
            date.plusDays(1).atTime(LocalTime.parse(shift.endTime))
        } else {
            date.atTime(LocalTime.parse(shift.endTime))
        }
        
        // Tìm thời gian thực tế từ logs
        val actualCheckIn = dayLogs.find { it.type == AttendanceType.CHECK_IN }?.time
        val actualCheckOut = dayLogs.find { it.type == AttendanceType.CHECK_OUT }?.time
        val actualDeparture = dayLogs.find { it.type == AttendanceType.DEPARTURE }?.time
        val actualArrival = dayLogs.find { it.type == AttendanceType.ARRIVAL }?.time
        
        // Xác định trạng thái tuân thủ
        val complianceStatus = determineComplianceStatus(shift, dayLogs, config)
        
        // Tính độ lệch thời gian
        val lateMinutes = if (actualCheckIn != null && actualCheckIn.isAfter(scheduledStart)) {
            ChronoUnit.MINUTES.between(scheduledStart, actualCheckIn).toInt()
        } else 0
        
        val earlyLeaveMinutes = if (actualCheckOut != null && actualCheckOut.isBefore(scheduledEnd)) {
            ChronoUnit.MINUTES.between(actualCheckOut, scheduledEnd).toInt()
        } else 0
        
        // Tính giờ công theo lịch trình (không phụ thuộc vào thời gian thực tế)
        val scheduledHours = calculateScheduledHours(shift, date, config)
        
        // Kiểm tra các điều kiện đặc biệt
        val isNightShift = isNightShiftWork(shift, date)
        val isHoliday = isHolidayWork(date, config)
        val isSunday = date.dayOfWeek.value == 7
        
        return WorkCalculation(
            date = date,
            shiftId = shift.id,
            scheduledStartTime = scheduledStart,
            scheduledEndTime = scheduledEnd,
            scheduledBreakMinutes = shift.breakMinutes,
            scheduledWorkMinutes = shift.getTotalWorkMinutes(),
            actualDepartureTime = actualDeparture,
            actualCheckInTime = actualCheckIn,
            actualCheckOutTime = actualCheckOut,
            actualArrivalTime = actualArrival,
            complianceStatus = complianceStatus,
            lateMinutes = lateMinutes,
            earlyLeaveMinutes = earlyLeaveMinutes,
            totalDeviationMinutes = lateMinutes + earlyLeaveMinutes,
            standardHours = scheduledHours["standard"] ?: 0.0,
            overtimeHours = scheduledHours["overtime"] ?: 0.0,
            nightShiftHours = scheduledHours["nightShift"] ?: 0.0,
            holidayHours = scheduledHours["holiday"] ?: 0.0,
            sundayHours = scheduledHours["sunday"] ?: 0.0,
            totalPaidHours = scheduledHours.values.sum(),
            isNightShift = isNightShift,
            isHoliday = isHoliday,
            isSunday = isSunday,
            hasBreakTime = shift.breakMinutes > 0,
            actualBreakMinutes = shift.breakMinutes // Simplified for now
        )
    }

    override suspend fun updateDailyWorkStatus(
        date: LocalDate,
        calculation: WorkCalculation
    ): DailyWorkStatus {
        return DailyWorkStatus(
            date = date,
            complianceStatus = calculation.complianceStatus,
            leaveStatus = null, // Will be set separately if needed
            appliedShiftIdForDay = calculation.shiftId,
            actualDepartureTime = calculation.actualDepartureTime,
            actualCheckInTime = calculation.actualCheckInTime,
            actualCheckOutTime = calculation.actualCheckOutTime,
            actualArrivalTime = calculation.actualArrivalTime,
            standardHoursScheduled = calculation.standardHours,
            otHoursScheduled = calculation.overtimeHours,
            sundayHoursScheduled = calculation.sundayHours,
            nightHoursScheduled = calculation.nightShiftHours,
            holidayHoursScheduled = calculation.holidayHours,
            totalHoursScheduled = calculation.totalPaidHours,
            lateMinutes = calculation.lateMinutes,
            earlyLeaveMinutes = calculation.earlyLeaveMinutes,
            overtimeMinutes = (calculation.overtimeHours * 60).toInt(),
            isHolidayWork = calculation.isHoliday,
            isNightShiftDay = calculation.isNightShift,
            lastCalculatedAt = LocalDateTime.now(),
            calculationVersion = 1
        )
    }

    override suspend fun calculateWorkSummary(
        fromDate: LocalDate,
        toDate: LocalDate,
        calculations: List<WorkCalculation>
    ): WorkSummary {
        val totalDays = ChronoUnit.DAYS.between(fromDate, toDate).toInt() + 1
        val workDays = calculations.size
        val presentDays = calculations.count { 
            it.complianceStatus != ComplianceStatus.MISSING_BOTH &&
            it.complianceStatus != ComplianceStatus.NOT_STARTED 
        }
        val absentDays = workDays - presentDays
        val lateDays = calculations.count { it.lateMinutes > 0 }
        val earlyDays = calculations.count { it.earlyLeaveMinutes > 0 }
        
        val totalStandardHours = calculations.sumOf { it.standardHours }
        val totalOvertimeHours = calculations.sumOf { it.overtimeHours }
        val totalNightShiftHours = calculations.sumOf { it.nightShiftHours }
        val totalHolidayHours = calculations.sumOf { it.holidayHours }
        val totalSundayHours = calculations.sumOf { it.sundayHours }
        val totalPaidHours = calculations.sumOf { it.totalPaidHours }
        
        val averageLateMinutes = if (lateDays > 0) {
            calculations.filter { it.lateMinutes > 0 }.map { it.lateMinutes }.average()
        } else 0.0
        
        val averageEarlyMinutes = if (earlyDays > 0) {
            calculations.filter { it.earlyLeaveMinutes > 0 }.map { it.earlyLeaveMinutes }.average()
        } else 0.0
        
        val complianceRate = if (workDays > 0) {
            (presentDays.toDouble() / workDays) * 100
        } else 0.0
        
        val punctualityScore = calculateComplianceScore(calculations)
        
        return WorkSummary(
            fromDate = fromDate,
            toDate = toDate,
            totalDays = totalDays,
            workDays = workDays,
            presentDays = presentDays,
            absentDays = absentDays,
            lateDays = lateDays,
            earlyDays = earlyDays,
            totalStandardHours = totalStandardHours,
            totalOvertimeHours = totalOvertimeHours,
            totalNightShiftHours = totalNightShiftHours,
            totalHolidayHours = totalHolidayHours,
            totalSundayHours = totalSundayHours,
            totalPaidHours = totalPaidHours,
            averageLateMinutes = averageLateMinutes,
            averageEarlyMinutes = averageEarlyMinutes,
            complianceRate = complianceRate,
            punctualityScore = punctualityScore
        )
    }

    override suspend fun determineComplianceStatus(
        shift: Shift,
        attendanceLogs: List<AttendanceLog>,
        config: WorkCalculationConfig
    ): ComplianceStatus {
        val checkIn = attendanceLogs.find { it.type == AttendanceType.CHECK_IN }
        val checkOut = attendanceLogs.find { it.type == AttendanceType.CHECK_OUT }
        
        return when {
            checkIn == null && checkOut == null -> ComplianceStatus.MISSING_BOTH
            checkIn == null -> ComplianceStatus.MISSING_CHECK_IN
            checkOut == null -> ComplianceStatus.MISSING_CHECK_OUT
            else -> {
                val scheduledStart = LocalTime.parse(shift.startTime)
                val scheduledEnd = LocalTime.parse(shift.endTime)
                val actualStart = checkIn.time.toLocalTime()
                val actualEnd = checkOut.time.toLocalTime()
                
                val isLate = actualStart.isAfter(scheduledStart.plusMinutes(config.lateThresholdMinutes.toLong()))
                val isEarly = actualEnd.isBefore(scheduledEnd.minusMinutes(config.earlyThresholdMinutes.toLong()))
                
                when {
                    isLate && isEarly -> ComplianceStatus.LATE_AND_EARLY
                    isLate -> ComplianceStatus.LATE_ARRIVAL
                    isEarly -> ComplianceStatus.EARLY_DEPARTURE
                    else -> ComplianceStatus.PERFECT_COMPLIANCE
                }
            }
        }
    }

    override suspend fun calculateScheduledHours(
        shift: Shift,
        date: LocalDate,
        config: WorkCalculationConfig
    ): Map<String, Double> {
        val baseHours = shift.getTotalWorkHours()
        val result = mutableMapOf<String, Double>()
        
        // Giờ công chuẩn
        result["standard"] = baseHours
        
        // Giờ ca đêm
        if (isNightShiftWork(shift, date)) {
            result["nightShift"] = baseHours
            result["standard"] = 0.0
        }
        
        // Giờ ngày lễ
        if (isHolidayWork(date, config)) {
            result["holiday"] = baseHours
            result["standard"] = 0.0
        }
        
        // Giờ chủ nhật
        if (date.dayOfWeek.value == 7) {
            result["sunday"] = baseHours
            result["standard"] = 0.0
        }
        
        // Làm thêm giờ (simplified)
        result["overtime"] = 0.0
        
        return result
    }

    override suspend fun isNightShiftWork(shift: Shift, date: LocalDate): Boolean {
        return shift.isNightShift
    }

    override suspend fun isHolidayWork(date: LocalDate, config: WorkCalculationConfig): Boolean {
        // Simplified implementation - would check against holiday calendar
        return false
    }

    override suspend fun calculateOvertime(
        shift: Shift,
        actualWorkMinutes: Int,
        config: WorkCalculationConfig
    ): Double {
        val scheduledMinutes = shift.getTotalWorkMinutes()
        val overtimeMinutes = max(0, actualWorkMinutes - scheduledMinutes)
        return overtimeMinutes / 60.0
    }

    override suspend fun roundTime(time: LocalDateTime, roundingMinutes: Int): LocalDateTime {
        if (roundingMinutes <= 0) return time
        
        val minutes = time.minute
        val roundedMinutes = (minutes / roundingMinutes) * roundingMinutes
        
        return time.withMinute(roundedMinutes).withSecond(0).withNano(0)
    }

    override suspend fun calculateComplianceScore(calculations: List<WorkCalculation>): Double {
        if (calculations.isEmpty()) return 0.0
        
        val scores = calculations.map { it.getEfficiencyScore() }
        return scores.average()
    }

    override suspend fun generateDetailedReport(
        fromDate: LocalDate,
        toDate: LocalDate,
        calculations: List<WorkCalculation>
    ): WorkReport {
        val summary = calculateWorkSummary(fromDate, toDate, calculations)
        val complianceAnalysis = analyzeCompliance(calculations)
        val recommendations = generateRecommendations(complianceAnalysis)
        
        return WorkReport(
            period = "$fromDate to $toDate",
            summary = summary,
            dailyDetails = calculations,
            complianceAnalysis = complianceAnalysis,
            recommendations = recommendations
        )
    }
    
    private suspend fun analyzeCompliance(calculations: List<WorkCalculation>): ComplianceAnalysis {
        val overallScore = calculateComplianceScore(calculations)
        val punctualityTrend = calculations.map { it.getEfficiencyScore() }
        
        // Analyze common issues
        val commonIssues = mutableListOf<ComplianceIssue>()
        
        val lateCount = calculations.count { it.lateMinutes > 0 }
        if (lateCount > calculations.size * 0.2) { // More than 20% late
            commonIssues.add(
                ComplianceIssue(
                    type = ComplianceIssueType.FREQUENT_LATE_ARRIVAL,
                    frequency = lateCount,
                    averageDeviation = calculations.filter { it.lateMinutes > 0 }.map { it.lateMinutes }.average(),
                    impact = ComplianceSeverity.MODERATE,
                    suggestion = "Cân nhắc điều chỉnh giờ đi làm sớm hơn 15-30 phút"
                )
            )
        }
        
        return ComplianceAnalysis(
            overallScore = overallScore,
            punctualityTrend = punctualityTrend,
            commonIssues = commonIssues,
            improvementAreas = listOf("Đúng giờ", "Tính nhất quán")
        )
    }
    
    private fun generateRecommendations(analysis: ComplianceAnalysis): List<String> {
        val recommendations = mutableListOf<String>()
        
        if (analysis.overallScore < 70) {
            recommendations.add("Cải thiện tính đúng giờ để nâng cao hiệu suất làm việc")
        }
        
        analysis.commonIssues.forEach { issue ->
            recommendations.add(issue.suggestion)
        }
        
        return recommendations
    }
}

package com.workly.app.integration

import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.workly.app.data.model.*
import com.workly.app.data.repository.*
import com.workly.app.services.*
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import javax.inject.Inject

/**
 * Integration test cho toàn bộ hệ thống Workly
 * Ki<PERSON><PERSON> tra tích hợp giữa các component
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class WorklyIntegrationTest {

    @get:Rule
    var hiltRule = HiltAndroidRule(this)

    @Inject
    lateinit var shiftRepository: ShiftRepository

    @Inject
    lateinit var attendanceRepository: AttendanceRepository

    @Inject
    lateinit var dailyWorkStatusRepository: DailyWorkStatusRepository

    @Inject
    lateinit var noteRepository: NoteRepository

    @Inject
    lateinit var settingsRepository: SettingsRepository

    @Inject
    lateinit var workCalculationRepository: WorkCalculationRepository

    @Inject
    lateinit var reminderSchedulingService: ReminderSchedulingService

    @Inject
    lateinit var shiftRotationService: ShiftRotationService

    @Inject
    lateinit var noteDisplayService: NoteDisplayService

    @Before
    fun setup() {
        hiltRule.inject()
    }

    @Test
    fun testCompleteWorkflowWithShiftRotation() = runTest {
        // 1. Tạo các ca làm việc
        val morningShift = createShift("morning", "Ca sáng", "08:00", "17:00")
        val eveningShift = createShift("evening", "Ca tối", "14:00", "23:00")
        val nightShift = createShift("night", "Ca đêm", "22:00", "06:00", isNightShift = true)

        shiftRepository.insertShift(morningShift)
        shiftRepository.insertShift(eveningShift)
        shiftRepository.insertShift(nightShift)

        // 2. Cấu hình xoay ca
        val rotationConfig = RotationConfig(
            rotationShifts = listOf("morning", "evening", "night"),
            rotationFrequency = RotationFrequency.WEEKLY,
            currentRotationIndex = 0,
            rotationLastAppliedDate = LocalDate.now().minusDays(7).toString()
        )

        val settings = UserSettings(
            activeShiftId = "morning",
            rotationConfig = rotationConfig
        )
        settingsRepository.updateUserSettings(settings)

        // 3. Kiểm tra xoay ca tự động
        val rotationResult = shiftRotationService.checkAndRotateShift()
        assertTrue("Rotation should be successful", rotationResult.success)
        assertEquals("evening", rotationResult.newShiftId)

        // 4. Tạo dữ liệu chấm công
        val today = LocalDate.now()
        val checkInLog = AttendanceLog(
            id = "checkin_${today}",
            type = AttendanceType.CHECK_IN,
            time = today.atTime(14, 5), // 5 minutes late
            workDate = today
        )
        val checkOutLog = AttendanceLog(
            id = "checkout_${today}",
            type = AttendanceType.CHECK_OUT,
            time = today.atTime(22, 55), // 5 minutes early
            workDate = today
        )

        attendanceRepository.insertAttendanceLog(checkInLog)
        attendanceRepository.insertAttendanceLog(checkOutLog)

        // 5. Tính toán công việc
        val calculation = workCalculationRepository.calculateAndUpdateDailyWork(
            date = today,
            shiftId = "evening",
            forceRecalculate = true
        )

        assertNotNull("Calculation should not be null", calculation)
        assertEquals(ComplianceStatus.LATE_AND_EARLY, calculation!!.complianceStatus)
        assertEquals(5, calculation.lateMinutes)
        assertEquals(5, calculation.earlyLeaveMinutes)
        assertEquals(8.0, calculation.standardHours, 0.01) // Giờ công theo lịch trình

        // 6. Kiểm tra DailyWorkStatus được cập nhật
        val dailyStatus = dailyWorkStatusRepository.getDailyWorkStatus(today)
        assertNotNull("Daily status should not be null", dailyStatus)
        assertEquals(ComplianceStatus.LATE_AND_EARLY, dailyStatus!!.complianceStatus)
        assertEquals(8.0, dailyStatus.totalHoursScheduled, 0.01)

        // 7. Tạo ghi chú với logic ưu tiên
        val priorityNote = Note(
            id = "priority_note",
            title = "Họp quan trọng",
            content = "Họp với khách hàng lúc 15:00",
            isPriority = true,
            isGuaranteedSlot = true,
            reminderDateTime = today.atTime(14, 45),
            enableNotifications = true,
            associatedShiftIds = listOf("evening"),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        val normalNote = Note(
            id = "normal_note",
            title = "Ghi chú thường",
            content = "Nhớ mua cà phê",
            isPriority = false,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        noteRepository.insertNote(priorityNote)
        noteRepository.insertNote(normalNote)

        // 8. Kiểm tra logic hiển thị ghi chú
        val displayedNotes = noteDisplayService.getNotesForHomeScreen(
            maxDisplayCount = 2,
            currentShiftId = "evening",
            currentDate = today
        )

        assertEquals(2, displayedNotes.size)
        assertEquals("priority_note", displayedNotes[0].id) // Priority note should be first
        assertTrue("Priority note should be displayed", 
            displayedNotes.any { it.id == "priority_note" })

        // 9. Kiểm tra nhắc nhở
        reminderSchedulingService.syncNextReminders()
        val scheduledReminders = reminderSchedulingService.getScheduledReminders()
        assertTrue("Should have scheduled reminders", scheduledReminders.isNotEmpty())

        // 10. Tạo báo cáo tổng hợp
        val summary = workCalculationRepository.getWorkSummary(
            fromDate = today.minusDays(6),
            toDate = today
        )

        assertEquals(1, summary.workDays)
        assertEquals(1, summary.presentDays)
        assertEquals(8.0, summary.totalStandardHours, 0.01)
        assertTrue("Compliance rate should be 100%", summary.complianceRate == 100.0)
    }

    @Test
    fun testWeatherWarningIntegration() = runTest {
        // Test sẽ được implement khi có WeatherService mock
        // Kiểm tra tích hợp cảnh báo thời tiết với ca làm việc
        assertTrue("Weather integration test placeholder", true)
    }

    @Test
    fun testNoteDisplayLogic() = runTest {
        val today = LocalDate.now()
        
        // Tạo các ghi chú với mức ưu tiên khác nhau
        val guaranteedNote = createNote("guaranteed", "Slot đảm bảo", isGuaranteedSlot = true)
        val priorityNote = createNote("priority", "Ưu tiên", isPriority = true)
        val pinnedNote = createNote("pinned", "Ghim", isPinned = true)
        val normalNote1 = createNote("normal1", "Bình thường 1")
        val normalNote2 = createNote("normal2", "Bình thường 2")

        listOf(guaranteedNote, priorityNote, pinnedNote, normalNote1, normalNote2)
            .forEach { noteRepository.insertNote(it) }

        // Test với giới hạn 3 ghi chú
        val displayedNotes = noteDisplayService.getNotesForHomeScreen(
            maxDisplayCount = 3,
            currentDate = today
        )

        assertEquals(3, displayedNotes.size)
        
        // Kiểm tra thứ tự ưu tiên
        assertEquals("guaranteed", displayedNotes[0].id) // Slot đảm bảo luôn đầu tiên
        assertTrue("Should contain priority note", 
            displayedNotes.any { it.id == "priority" })
        
        // Ghi chú bình thường có thể bị loại bỏ nếu hết slot
        val normalNotesDisplayed = displayedNotes.count { 
            it.id.startsWith("normal") 
        }
        assertTrue("Normal notes should be limited", normalNotesDisplayed <= 1)
    }

    // Helper methods
    private fun createShift(
        id: String,
        name: String,
        startTime: String,
        endTime: String,
        isNightShift: Boolean = false
    ): Shift {
        return Shift(
            id = id,
            name = name,
            startTime = startTime,
            endTime = endTime,
            departureTime = LocalTime.parse(startTime).minusMinutes(30).toString(),
            maxEndTime = endTime,
            daysApplied = listOf("Mon", "Tue", "Wed", "Thu", "Fri"),
            isNightShift = isNightShift,
            breakMinutes = 60,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }

    private fun createNote(
        id: String,
        title: String,
        isPriority: Boolean = false,
        isGuaranteedSlot: Boolean = false,
        isPinned: Boolean = false
    ): Note {
        return Note(
            id = id,
            title = title,
            content = "Test content for $title",
            isPriority = isPriority,
            isGuaranteedSlot = isGuaranteedSlot,
            isPinned = isPinned,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }
}

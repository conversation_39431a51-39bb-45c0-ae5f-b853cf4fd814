package com.workly.app.workers

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.*
import com.workly.app.data.model.*
import com.workly.app.data.repository.SettingsRepository
import com.workly.app.data.repository.ShiftRepository
import com.workly.app.services.NotificationService
import com.workly.app.services.WeatherService
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.first
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.concurrent.TimeUnit

/**
 * Worker chạy nền để kiểm tra cảnh báo thời tiết ngữ cảnh
 * Chạy ~1h trước departureTime để cảnh báo cho cả chiều đi và về
 */
@HiltWorker
class WeatherWarningWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val weatherService: WeatherService,
    private val notificationService: NotificationService,
    private val settingsRepository: SettingsRepository,
    private val shiftRepository: ShiftRepository
) : CoroutineWorker(context, workerParams) {

    override suspend fun doWork(): Result {
        return try {
            val settings = settingsRepository.getUserSettings().first()
            
            // Kiểm tra xem có bật cảnh báo thời tiết không
            if (!settings.weatherWarningEnabled) {
                return Result.success()
            }
            
            // Lấy thông tin ca hiện tại
            val activeShift = settings.activeShiftId?.let { 
                shiftRepository.getShiftById(it) 
            } ?: return Result.success()
            
            // Lấy vị trí nhà và công ty
            val homeLocation = settings.homeLocation?.let {
                LocationCoordinate(it.latitude, it.longitude)
            } ?: return Result.success()
            
            val workLocation = settings.workLocation?.let {
                LocationCoordinate(it.latitude, it.longitude)
            } ?: return Result.success()
            
            // Tính thời gian đi và về
            val now = LocalDateTime.now()
            val today = now.toLocalDate()
            
            val departureTime = today.atTime(LocalTime.parse(activeShift.departureTime))
            val returnTime = if (activeShift.isNightShift) {
                // Ca đêm: thời gian về là ngày hôm sau
                today.plusDays(1).atTime(LocalTime.parse(activeShift.maxEndTime))
            } else {
                today.atTime(LocalTime.parse(activeShift.maxEndTime))
            }
            
            // Kiểm tra cảnh báo thời tiết ngữ cảnh
            val contextualWarning = weatherService.checkContextualWeatherWarnings(
                homeLocation = homeLocation,
                workLocation = workLocation,
                departureTime = departureTime,
                returnTime = returnTime
            )
            
            // Gửi thông báo nếu có cảnh báo
            if (contextualWarning.isValid && 
                contextualWarning.overallSeverity != WeatherWarningSeverity.NONE) {
                sendWeatherNotification(contextualWarning, activeShift)
            }
            
            Result.success(
                workDataOf(
                    "warning_sent" to (contextualWarning.overallSeverity != WeatherWarningSeverity.NONE),
                    "severity" to contextualWarning.overallSeverity.name,
                    "message" to contextualWarning.primaryMessage
                )
            )
            
        } catch (e: Exception) {
            Result.failure(
                workDataOf(
                    "error" to (e.message ?: "Unknown error")
                )
            )
        }
    }
    
    private suspend fun sendWeatherNotification(
        warning: ContextualWeatherWarning,
        shift: Shift
    ) {
        val title = when (warning.overallSeverity) {
            WeatherWarningSeverity.LOW -> "⚠️ Chú ý thời tiết"
            WeatherWarningSeverity.MODERATE -> "🌧️ Cảnh báo thời tiết"
            WeatherWarningSeverity.HIGH -> "⛈️ Cảnh báo thời tiết nghiêm trọng"
            WeatherWarningSeverity.CRITICAL -> "🚨 Cảnh báo thời tiết nguy hiểm"
            else -> "🌤️ Thông tin thời tiết"
        }
        
        val message = buildString {
            append(warning.primaryMessage)
            
            // Thêm thông tin cụ thể cho chiều đi
            warning.departureWarning?.let { departure ->
                if (departure.warnings.isNotEmpty()) {
                    append("\n\n🏠➡️🏢 Đi làm: ")
                    append(departure.warnings.first().message)
                }
            }
            
            // Thêm thông tin cụ thể cho chiều về
            warning.returnWarning?.let { return_ ->
                if (return_.warnings.isNotEmpty()) {
                    append("\n\n🏢➡️🏠 Về nhà: ")
                    append(return_.warnings.first().message)
                }
            }
            
            // Thêm khuyến nghị
            if (warning.actionRecommendations.isNotEmpty()) {
                append("\n\n💡 Khuyến nghị:")
                warning.actionRecommendations.take(3).forEach { recommendation ->
                    append("\n• $recommendation")
                }
            }
        }
        
        notificationService.showInstantNotification(
            title = title,
            message = message,
            channelId = "weather_warnings"
        )
    }

    companion object {
        const val WORK_NAME = "weather_warning_check"
        
        /**
         * Lên lịch kiểm tra cảnh báo thời tiết
         * Chạy mỗi ngày vào 1h trước giờ đi làm
         */
        fun scheduleWeatherCheck(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .setRequiresBatteryNotLow(false)
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false)
                .setRequiresStorageNotLow(false)
                .build()

            val periodicWorkRequest = PeriodicWorkRequestBuilder<WeatherWarningWorker>(
                repeatInterval = 1,
                repeatIntervalTimeUnit = TimeUnit.DAYS,
                flexTimeInterval = 30, // Linh hoạt trong vòng 30 phút
                flexTimeIntervalUnit = TimeUnit.MINUTES
            )
                .setConstraints(constraints)
                .setInitialDelay(calculateInitialDelay(), TimeUnit.MINUTES)
                .addTag("weather_warning")
                .build()

            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    WORK_NAME,
                    ExistingPeriodicWorkPolicy.REPLACE,
                    periodicWorkRequest
                )
        }
        
        /**
         * Hủy kiểm tra cảnh báo thời tiết
         */
        fun cancelWeatherCheck(context: Context) {
            WorkManager.getInstance(context)
                .cancelUniqueWork(WORK_NAME)
        }
        
        /**
         * Chạy kiểm tra thời tiết ngay lập tức
         */
        fun runImmediateCheck(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()

            val immediateWorkRequest = OneTimeWorkRequestBuilder<WeatherWarningWorker>()
                .setConstraints(constraints)
                .addTag("weather_warning_immediate")
                .build()

            WorkManager.getInstance(context)
                .enqueueUniqueWork(
                    "weather_warning_immediate",
                    ExistingWorkPolicy.REPLACE,
                    immediateWorkRequest
                )
        }
        
        /**
         * Tính toán delay ban đầu để chạy vào 1h trước giờ đi làm
         */
        private fun calculateInitialDelay(): Long {
            val now = LocalDateTime.now()
            val tomorrow = now.toLocalDate().plusDays(1)
            
            // Mặc định chạy lúc 6:30 sáng (1h trước 7:30)
            val targetTime = tomorrow.atTime(6, 30)
            
            val delayMinutes = java.time.Duration.between(now, targetTime).toMinutes()
            return if (delayMinutes > 0) delayMinutes else 0
        }
    }
}

/**
 * Worker để refresh dữ liệu thời tiết định kỳ
 */
@HiltWorker
class WeatherRefreshWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val weatherService: WeatherService,
    private val settingsRepository: SettingsRepository
) : CoroutineWorker(context, workerParams) {

    override suspend fun doWork(): Result {
        return try {
            val settings = settingsRepository.getUserSettings().first()
            
            if (settings.weatherWarningEnabled) {
                weatherService.refreshWeatherData()
            }
            
            Result.success()
            
        } catch (e: Exception) {
            Result.retry()
        }
    }

    companion object {
        const val WORK_NAME = "weather_refresh"
        
        fun schedulePeriodicRefresh(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .setRequiresBatteryNotLow(true)
                .build()

            val periodicWorkRequest = PeriodicWorkRequestBuilder<WeatherRefreshWorker>(
                repeatInterval = 1,
                repeatIntervalTimeUnit = TimeUnit.HOURS
            )
                .setConstraints(constraints)
                .addTag("weather_refresh")
                .build()

            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    WORK_NAME,
                    ExistingPeriodicWorkPolicy.KEEP,
                    periodicWorkRequest
                )
        }
        
        fun cancelPeriodicRefresh(context: Context) {
            WorkManager.getInstance(context)
                .cancelUniqueWork(WORK_NAME)
        }
    }
}

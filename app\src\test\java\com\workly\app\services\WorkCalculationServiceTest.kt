package com.workly.app.services

import com.workly.app.data.model.*
import com.workly.app.data.repository.SettingsRepository
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Test cho WorkCalculationService
 * Kiểm tra logic tính công minh bạch
 */
class WorkCalculationServiceTest {

    @Mock
    private lateinit var settingsRepository: SettingsRepository

    private lateinit var workCalculationService: WorkCalculationService

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        workCalculationService = WorkCalculationServiceImpl(settingsRepository)
    }

    @Test
    fun `test perfect compliance calculation`() = runTest {
        // Arrange
        val date = LocalDate.of(2025, 7, 2)
        val shift = createTestShift(
            startTime = "08:00",
            endTime = "17:00",
            breakMinutes = 60
        )
        
        val attendanceLogs = listOf(
            createAttendanceLog(AttendanceType.CHECK_IN, date.atTime(8, 0)),
            createAttendanceLog(AttendanceType.CHECK_OUT, date.atTime(17, 0))
        )
        
        val config = createTestConfig()

        // Act
        val result = workCalculationService.calculateDailyWork(date, shift, attendanceLogs, config)

        // Assert
        assertEquals(ComplianceStatus.PERFECT_COMPLIANCE, result.complianceStatus)
        assertEquals(0, result.lateMinutes)
        assertEquals(0, result.earlyLeaveMinutes)
        assertEquals(8.0, result.standardHours, 0.01) // 9 hours - 1 hour break
    }

    @Test
    fun `test late arrival calculation`() = runTest {
        // Arrange
        val date = LocalDate.of(2025, 7, 2)
        val shift = createTestShift(
            startTime = "08:00",
            endTime = "17:00"
        )
        
        val attendanceLogs = listOf(
            createAttendanceLog(AttendanceType.CHECK_IN, date.atTime(8, 30)), // 30 minutes late
            createAttendanceLog(AttendanceType.CHECK_OUT, date.atTime(17, 0))
        )
        
        val config = createTestConfig(lateThresholdMinutes = 15)

        // Act
        val result = workCalculationService.calculateDailyWork(date, shift, attendanceLogs, config)

        // Assert
        assertEquals(ComplianceStatus.LATE_ARRIVAL, result.complianceStatus)
        assertEquals(30, result.lateMinutes)
        assertEquals(0, result.earlyLeaveMinutes)
        // Giờ công vẫn tính theo lịch trình, không phụ thuộc thời gian thực tế
        assertEquals(8.0, result.standardHours, 0.01)
    }

    @Test
    fun `test early departure calculation`() = runTest {
        // Arrange
        val date = LocalDate.of(2025, 7, 2)
        val shift = createTestShift(
            startTime = "08:00",
            endTime = "17:00"
        )
        
        val attendanceLogs = listOf(
            createAttendanceLog(AttendanceType.CHECK_IN, date.atTime(8, 0)),
            createAttendanceLog(AttendanceType.CHECK_OUT, date.atTime(16, 30)) // 30 minutes early
        )
        
        val config = createTestConfig(earlyThresholdMinutes = 15)

        // Act
        val result = workCalculationService.calculateDailyWork(date, shift, attendanceLogs, config)

        // Assert
        assertEquals(ComplianceStatus.EARLY_DEPARTURE, result.complianceStatus)
        assertEquals(0, result.lateMinutes)
        assertEquals(30, result.earlyLeaveMinutes)
        // Giờ công vẫn tính theo lịch trình
        assertEquals(8.0, result.standardHours, 0.01)
    }

    @Test
    fun `test missing check-in calculation`() = runTest {
        // Arrange
        val date = LocalDate.of(2025, 7, 2)
        val shift = createTestShift()
        
        val attendanceLogs = listOf(
            createAttendanceLog(AttendanceType.CHECK_OUT, date.atTime(17, 0))
        )
        
        val config = createTestConfig()

        // Act
        val result = workCalculationService.calculateDailyWork(date, shift, attendanceLogs, config)

        // Assert
        assertEquals(ComplianceStatus.MISSING_CHECK_IN, result.complianceStatus)
        // Vẫn tính giờ công theo lịch trình
        assertEquals(8.0, result.standardHours, 0.01)
    }

    @Test
    fun `test night shift calculation`() = runTest {
        // Arrange
        val date = LocalDate.of(2025, 7, 2)
        val shift = createTestShift(
            startTime = "22:00",
            endTime = "06:00",
            isNightShift = true
        )
        
        val attendanceLogs = listOf(
            createAttendanceLog(AttendanceType.CHECK_IN, date.atTime(22, 0)),
            createAttendanceLog(AttendanceType.CHECK_OUT, date.plusDays(1).atTime(6, 0))
        )
        
        val config = createTestConfig()

        // Act
        val result = workCalculationService.calculateDailyWork(date, shift, attendanceLogs, config)

        // Assert
        assertEquals(ComplianceStatus.PERFECT_COMPLIANCE, result.complianceStatus)
        assertTrue(result.isNightShift)
        assertEquals(8.0, result.nightShiftHours, 0.01)
        assertEquals(0.0, result.standardHours, 0.01) // Night shift hours replace standard hours
    }

    @Test
    fun `test work summary calculation`() = runTest {
        // Arrange
        val fromDate = LocalDate.of(2025, 7, 1)
        val toDate = LocalDate.of(2025, 7, 5)
        
        val calculations = listOf(
            createWorkCalculation(LocalDate.of(2025, 7, 1), ComplianceStatus.PERFECT_COMPLIANCE, 8.0),
            createWorkCalculation(LocalDate.of(2025, 7, 2), ComplianceStatus.LATE_ARRIVAL, 8.0, lateMinutes = 15),
            createWorkCalculation(LocalDate.of(2025, 7, 3), ComplianceStatus.PERFECT_COMPLIANCE, 8.0),
            createWorkCalculation(LocalDate.of(2025, 7, 4), ComplianceStatus.EARLY_DEPARTURE, 8.0, earlyMinutes = 20),
            createWorkCalculation(LocalDate.of(2025, 7, 5), ComplianceStatus.PERFECT_COMPLIANCE, 8.0)
        )

        // Act
        val summary = workCalculationService.calculateWorkSummary(fromDate, toDate, calculations)

        // Assert
        assertEquals(5, summary.totalDays)
        assertEquals(5, summary.workDays)
        assertEquals(5, summary.presentDays)
        assertEquals(0, summary.absentDays)
        assertEquals(1, summary.lateDays)
        assertEquals(1, summary.earlyDays)
        assertEquals(40.0, summary.totalStandardHours, 0.01)
        assertEquals(15.0, summary.averageLateMinutes, 0.01)
        assertEquals(20.0, summary.averageEarlyMinutes, 0.01)
        assertEquals(100.0, summary.complianceRate, 0.01) // All days present
    }

    // Helper methods
    private fun createTestShift(
        startTime: String = "08:00",
        endTime: String = "17:00",
        breakMinutes: Int = 60,
        isNightShift: Boolean = false
    ): Shift {
        return Shift(
            id = "test-shift",
            name = "Test Shift",
            startTime = startTime,
            endTime = endTime,
            departureTime = "07:30",
            maxEndTime = endTime,
            daysApplied = listOf("Mon", "Tue", "Wed", "Thu", "Fri"),
            breakMinutes = breakMinutes,
            isNightShift = isNightShift,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }

    private fun createAttendanceLog(
        type: AttendanceType,
        time: LocalDateTime
    ): AttendanceLog {
        return AttendanceLog(
            id = "test-log-${System.currentTimeMillis()}",
            type = type,
            time = time,
            workDate = time.toLocalDate()
        )
    }

    private fun createTestConfig(
        lateThresholdMinutes: Int = 15,
        earlyThresholdMinutes: Int = 15
    ): WorkCalculationConfig {
        return WorkCalculationConfig(
            lateThresholdMinutes = lateThresholdMinutes,
            earlyThresholdMinutes = earlyThresholdMinutes,
            overtimeThresholdMinutes = 30,
            roundingMinutes = 1,
            overtimeMultiplier = 1.5,
            nightShiftMultiplier = 1.3,
            holidayMultiplier = 2.0
        )
    }

    private fun createWorkCalculation(
        date: LocalDate,
        complianceStatus: ComplianceStatus,
        standardHours: Double,
        lateMinutes: Int = 0,
        earlyMinutes: Int = 0
    ): WorkCalculation {
        return WorkCalculation(
            date = date,
            shiftId = "test-shift",
            scheduledStartTime = date.atTime(8, 0),
            scheduledEndTime = date.atTime(17, 0),
            scheduledBreakMinutes = 60,
            scheduledWorkMinutes = 480,
            actualCheckInTime = date.atTime(8, lateMinutes),
            actualCheckOutTime = date.atTime(17, 0).minusMinutes(earlyMinutes.toLong()),
            complianceStatus = complianceStatus,
            lateMinutes = lateMinutes,
            earlyLeaveMinutes = earlyMinutes,
            totalDeviationMinutes = lateMinutes + earlyMinutes,
            standardHours = standardHours,
            totalPaidHours = standardHours
        )
    }
}

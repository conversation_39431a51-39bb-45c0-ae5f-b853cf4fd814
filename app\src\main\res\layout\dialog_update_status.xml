<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:id="@+id/text_view_dialog_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
            android:textColor="?attr/colorOnSurface"
            tools:text="Cập nhật trạng thái" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button_close"
            style="@style/Widget.Material3.Button.IconButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            app:icon="@drawable/ic_close"
            app:iconSize="24dp" />

    </LinearLayout>

    <!-- Date Info -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="?attr/colorSurfaceVariant">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:src="@drawable/ic_calendar"
                app:tint="?attr/colorOnSurfaceVariant" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/text_view_selected_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="Thứ Hai, 02/07/2025" />

                <TextView
                    android:id="@+id/text_view_current_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="Trạng thái hiện tại: Hoàn thành" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Update Type Selection -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="Chọn loại cập nhật:"
        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
        android:textColor="?attr/colorOnSurface" />

    <RadioGroup
        android:id="@+id/radio_group_update_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <com.google.android.material.radiobutton.MaterialRadioButton
            android:id="@+id/radio_set_leave_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Đặt trạng thái nghỉ"
            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
            android:padding="12dp" />

        <com.google.android.material.radiobutton.MaterialRadioButton
            android:id="@+id/radio_edit_attendance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Chỉnh sửa giờ chấm công"
            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
            android:padding="12dp" />

    </RadioGroup>

    <!-- Content Container (Dynamic) -->
    <FrameLayout
        android:id="@+id/container_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp">

        <!-- Leave Status Selection (Initially Hidden) -->
        <LinearLayout
            android:id="@+id/layout_leave_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="Chọn loại nghỉ:"
                android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                android:textColor="?attr/colorOnSurface" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_leave_types"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:itemCount="5"
                tools:listitem="@layout/item_leave_type" />

        </LinearLayout>

        <!-- Attendance Edit (Initially Hidden) -->
        <LinearLayout
            android:id="@+id/layout_edit_attendance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="Chỉnh sửa thời gian chấm công:"
                android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
                android:textColor="?attr/colorOnSurface" />

            <!-- Check-in Time -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/text_input_layout_check_in"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:hint="Giờ vào làm"
                app:startIconDrawable="@drawable/ic_login"
                app:endIconMode="custom"
                app:endIconDrawable="@drawable/ic_time">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_check_in_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:clickable="true"
                    android:inputType="none"
                    tools:text="08:30" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Check-out Time -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/text_input_layout_check_out"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:hint="Giờ ra về"
                app:startIconDrawable="@drawable/ic_logout"
                app:endIconMode="custom"
                app:endIconDrawable="@drawable/ic_time">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_check_out_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:clickable="true"
                    android:inputType="none"
                    tools:text="17:30" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Validation Messages -->
            <LinearLayout
                android:id="@+id/layout_validation_messages"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/text_view_validation_error"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:padding="12dp"
                    android:background="@drawable/bg_error_message"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="?attr/colorError"
                    android:drawableStart="@drawable/ic_error"
                    android:drawablePadding="8dp"
                    android:visibility="gone"
                    tools:text="Giờ ra về phải sau giờ vào làm"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/text_view_validation_warning"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:padding="12dp"
                    android:background="@drawable/bg_warning_message"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:drawableStart="@drawable/ic_warning"
                    android:drawablePadding="8dp"
                    android:visibility="gone"
                    tools:text="Thời gian này khác xa so với lịch trình ca"
                    tools:visibility="visible" />

            </LinearLayout>

        </LinearLayout>

    </FrameLayout>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button_cancel"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="Hủy" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/button_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Lưu"
            android:enabled="false" />

    </LinearLayout>

</LinearLayout>

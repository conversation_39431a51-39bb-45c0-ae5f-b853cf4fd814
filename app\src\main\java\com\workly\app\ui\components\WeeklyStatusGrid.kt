package com.workly.app.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.workly.app.data.model.WeeklyDisplayItem

/**
 * Lưới trạng thái tuần hiển thị 7 ô từ T2 đến CN
 */
@Composable
fun WeeklyStatusGrid(
    weeklyItems: List<WeeklyDisplayItem>,
    onItemClick: (WeeklyDisplayItem) -> Unit = {},
    onItemLongClick: (WeeklyDisplayItem) -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(text = "Weekly Status Grid")
            Text(text = "Items: ${weeklyItems.size}")
        }
    }
}

// Simplified placeholder functions
@Composable
fun UpdateStatusDialog(
    item: WeeklyDisplayItem,
    onDismiss: () -> Unit,
    onStatusUpdate: (com.workly.app.data.model.WeeklyDisplayStatus) -> Unit
) {
    // Placeholder
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DayDetailBottomSheet(
    item: WeeklyDisplayItem,
    attendanceLogs: List<com.workly.app.data.model.AttendanceLog> = emptyList(),
    onDismiss: () -> Unit,
    onEditStatus: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    // Placeholder
}

/**
 * Chú thích các icon
 */
@Composable
private fun WeeklyStatusLegend() {
    Column {
        Text(
            text = "Chú thích:",
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // Row 1
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            LegendItem("✅", "Đủ công")
            LegendItem("❗", "Thiếu log")
            LegendItem("RV", "Muộn/Sớm")
            LegendItem("📩", "Nghỉ phép")
        }
        
        Spacer(modifier = Modifier.height(2.dp))
        
        // Row 2
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            LegendItem("🛌", "Nghỉ bệnh")
            LegendItem("🎌", "Nghỉ lễ")
            LegendItem("❌", "Vắng mặt")
            LegendItem("❓", "Chưa cập nhật")
        }
    }
}

/**
 * Item chú thích
 */
@Composable
private fun LegendItem(
    icon: String,
    text: String
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(end = 8.dp)
    ) {
        Text(
            text = icon,
            fontSize = 10.sp,
            modifier = Modifier.padding(end = 2.dp)
        )
        Text(
            text = text,
            fontSize = 9.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * Dialog để cập nhật trạng thái cho ngày tương lai
 */
@Composable
fun UpdateStatusDialog(
    item: WeeklyDisplayItem,
    onDismiss: () -> Unit,
    onStatusUpdate: (WeeklyDisplayStatus) -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("Cập nhật trạng thái")
        },
        text = {
            Column {
                Text("Ngày: ${item.dayOfWeek} ${item.dayNumber}")
                Spacer(modifier = Modifier.height(8.dp))
                Text("Chọn trạng thái:")

                // Các option cho ngày tương lai
                val futureStatuses = listOf(
                    WeeklyDisplayStatus.NGHI_PHEP,
                    WeeklyDisplayStatus.NGHI_BENH,
                    WeeklyDisplayStatus.NGHI_LE,
                    WeeklyDisplayStatus.VANG_MAT
                )

                futureStatuses.forEach { status ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onStatusUpdate(status) }
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = status.getDisplayIcon(),
                            modifier = Modifier.padding(end = 8.dp)
                        )
                        Text(text = status.getDisplayText())
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Đóng")
            }
        }
    )
}

/**
 * Bottom Sheet hiển thị chi tiết ngày
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DayDetailBottomSheet(
    item: WeeklyDisplayItem,
    attendanceLogs: List<com.workly.app.data.model.AttendanceLog> = emptyList(),
    onDismiss: () -> Unit,
    onEditStatus: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Chi tiết ${item.dayOfWeek} ${item.dayNumber}",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )

                if (item.isFuture) {
                    TextButton(onClick = onEditStatus) {
                        Text("Sửa")
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Status Display
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = item.status.getDisplayIcon(),
                    fontSize = 24.sp
                )
                Column {
                    Text(
                        text = item.status.getDisplayText(),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "Trạng thái: ${item.status.name}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Attendance Logs
            if (attendanceLogs.isNotEmpty()) {
                Text(
                    text = "Lịch sử chấm công:",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )

                Spacer(modifier = Modifier.height(8.dp))

                attendanceLogs.forEach { log ->
                    AttendanceLogItem(log = log)
                }
            } else if (!item.isFuture) {
                Text(
                    text = "Không có dữ liệu chấm công",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(24.dp))
        }
    }
}

/**
 * Item hiển thị log chấm công
 */
@Composable
private fun AttendanceLogItem(
    log: com.workly.app.data.model.AttendanceLog
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = log.getDisplayText().split(" ")[0], // Lấy icon
                fontSize = 16.sp
            )
            Text(
                text = log.getDisplayText().substringAfter(" "), // Lấy text
                style = MaterialTheme.typography.bodyMedium
            )
        }

        Text(
            text = log.time.format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss")),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

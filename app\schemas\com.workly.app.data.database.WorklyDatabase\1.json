{"formatVersion": 1, "database": {"version": 1, "identityHash": "afaefea0d2c6a0a87f7898fc4c998d35", "entities": [{"tableName": "shifts", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `description` TEXT, `startTime` TEXT NOT NULL, `endTime` TEXT NOT NULL, `departureTime` TEXT NOT NULL, `maxEndTime` TEXT NOT NULL, `daysApplied` TEXT NOT NULL, `workDays` TEXT NOT NULL, `isNightShift` INTEGER NOT NULL, `nightShiftStartDate` TEXT, `breakMinutes` INTEGER NOT NULL, `breakStartTime` TEXT, `breakEndTime` TEXT, `isFlexibleBreak` INTEGER NOT NULL, `remindBeforeDeparture` INTEGER NOT NULL, `remindBeforeStart` INTEGER NOT NULL, `remindAfterEnd` INTEGER NOT NULL, `enableDepartureReminder` INTEGER NOT NULL, `enableStartReminder` INTEGER NOT NULL, `enableEndReminder` INTEGER NOT NULL, `showPunch` INTEGER NOT NULL, `requireLocationCheck` INTEGER NOT NULL, `autoCheckInRadius` INTEGER NOT NULL, `hourlyRate` REAL NOT NULL, `overtimeMultiplier` REAL NOT NULL, `nightShiftMultiplier` REAL NOT NULL, `holidayMultiplier` REAL NOT NULL, `isActive` INTEGER NOT NULL, `sortOrder` INTEGER NOT NULL, `createdAt` TEXT NOT NULL, `updatedAt` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "startTime", "columnName": "startTime", "affinity": "TEXT", "notNull": true}, {"fieldPath": "endTime", "columnName": "endTime", "affinity": "TEXT", "notNull": true}, {"fieldPath": "departureTime", "columnName": "departureTime", "affinity": "TEXT", "notNull": true}, {"fieldPath": "maxEndTime", "columnName": "maxEndTime", "affinity": "TEXT", "notNull": true}, {"fieldPath": "daysApplied", "columnName": "daysApplied", "affinity": "TEXT", "notNull": true}, {"fieldPath": "workDays", "columnName": "workDays", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isNightShift", "columnName": "isNightShift", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "nightShiftStartDate", "columnName": "nightShiftStartDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "breakMinutes", "columnName": "breakMinutes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "breakStartTime", "columnName": "breakStartTime", "affinity": "TEXT", "notNull": false}, {"fieldPath": "breakEndTime", "columnName": "breakEndTime", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isFlexibleBreak", "columnName": "isFlexibleBreak", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "remindBeforeDeparture", "columnName": "remindBeforeDeparture", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "remindBeforeStart", "columnName": "remindBeforeStart", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "remindAfterEnd", "columnName": "remindAfterEnd", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "enableDepartureReminder", "columnName": "enableDepartureReminder", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "enableStartReminder", "columnName": "enableStartReminder", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "enableEndReminder", "columnName": "enableEndReminder", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "showPunch", "columnName": "showPunch", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "requireLocationCheck", "columnName": "requireLocationCheck", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "autoCheckInRadius", "columnName": "autoCheckInRadius", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hourlyRate", "columnName": "hourlyRate", "affinity": "REAL", "notNull": true}, {"fieldPath": "overtimeMultiplier", "columnName": "overtimeMultiplier", "affinity": "REAL", "notNull": true}, {"fieldPath": "nightShiftMultiplier", "columnName": "nightShiftMultiplier", "affinity": "REAL", "notNull": true}, {"fieldPath": "holidayMultiplier", "columnName": "holidayMultiplier", "affinity": "REAL", "notNull": true}, {"fieldPath": "isActive", "columnName": "isActive", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sortOrder", "columnName": "sortOrder", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "TEXT", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "attendance_logs", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `type` TEXT NOT NULL, `time` TEXT NOT NULL, `originalTimestamp` TEXT NOT NULL, `shiftId` TEXT, `workDate` TEXT NOT NULL, `location` TEXT, `latitude` REAL, `longitude` REAL, `locationAccuracy` REAL, `isAutoGenerated` INTEGER NOT NULL, `isManualEntry` INTEGER NOT NULL, `isLocationVerified` INTEGER NOT NULL, `deviceInfo` TEXT, `note` TEXT, `isDeleted` INTEGER NOT NULL, `createdAt` TEXT NOT NULL, `updatedAt` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "time", "columnName": "time", "affinity": "TEXT", "notNull": true}, {"fieldPath": "originalTimestamp", "columnName": "originalTimestamp", "affinity": "TEXT", "notNull": true}, {"fieldPath": "shiftId", "columnName": "shiftId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "workDate", "columnName": "workDate", "affinity": "TEXT", "notNull": true}, {"fieldPath": "location", "columnName": "location", "affinity": "TEXT", "notNull": false}, {"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": false}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": false}, {"fieldPath": "locationAccuracy", "columnName": "locationAccuracy", "affinity": "REAL", "notNull": false}, {"fieldPath": "isAutoGenerated", "columnName": "isAutoGenerated", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isManualEntry", "columnName": "isManualEntry", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isLocationVerified", "columnName": "isLocationVerified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deviceInfo", "columnName": "deviceInfo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "note", "columnName": "note", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isDeleted", "columnName": "isDeleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "TEXT", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "notes", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `content` TEXT NOT NULL, `isPriority` INTEGER NOT NULL, `isGuaranteedSlot` INTEGER NOT NULL, `isPinned` INTEGER NOT NULL, `reminderDateTime` TEXT, `enableNotifications` INTEGER NOT NULL, `notificationAdvanceMinutes` INTEGER NOT NULL, `remindForShifts` INTEGER NOT NULL, `associatedShiftIds` TEXT NOT NULL, `associatedDates` TEXT NOT NULL, `tags` TEXT NOT NULL, `isCompleted` INTEGER NOT NULL, `isArchived` INTEGER NOT NULL, `isHiddenFromHome` INTEGER NOT NULL, `snoozeUntil` TEXT, `repeatType` TEXT NOT NULL, `repeatInterval` INTEGER NOT NULL, `repeatEndDate` TEXT, `repeatDaysOfWeek` TEXT NOT NULL, `category` TEXT NOT NULL, `color` TEXT, `icon` TEXT, `createdAt` TEXT NOT NULL, `updatedAt` TEXT NOT NULL, `lastViewedAt` TEXT, `viewCount` INTEGER NOT NULL, `sortOrder` INTEGER NOT NULL, `location` TEXT, `attachments` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isPriority", "columnName": "isPriority", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isGuaranteedSlot", "columnName": "isGuaranteedSlot", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isPinned", "columnName": "isPinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reminderDateTime", "columnName": "reminderDateTime", "affinity": "TEXT", "notNull": false}, {"fieldPath": "enableNotifications", "columnName": "enableNotifications", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notificationAdvanceMinutes", "columnName": "notificationAdvanceMinutes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "remindForShifts", "columnName": "remindForShifts", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "associatedShiftIds", "columnName": "associatedShiftIds", "affinity": "TEXT", "notNull": true}, {"fieldPath": "associatedDates", "columnName": "associatedDates", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tags", "columnName": "tags", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isCompleted", "columnName": "isCompleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isArchived", "columnName": "isArchived", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isHiddenFromHome", "columnName": "isHiddenFromHome", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "snoozeUntil", "columnName": "snoozeUntil", "affinity": "TEXT", "notNull": false}, {"fieldPath": "repeatType", "columnName": "repeatType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "repeatInterval", "columnName": "repeatInterval", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "repeatEndDate", "columnName": "repeatEndDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "repeatDaysOfWeek", "columnName": "repeatDaysOfWeek", "affinity": "TEXT", "notNull": true}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT", "notNull": true}, {"fieldPath": "color", "columnName": "color", "affinity": "TEXT", "notNull": false}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "TEXT", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastViewedAt", "columnName": "lastViewedAt", "affinity": "TEXT", "notNull": false}, {"fieldPath": "viewCount", "columnName": "viewCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sortOrder", "columnName": "sortOrder", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "location", "columnName": "location", "affinity": "TEXT", "notNull": false}, {"fieldPath": "attachments", "columnName": "attachments", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "daily_work_status", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`date` TEXT NOT NULL, `complianceStatus` TEXT NOT NULL, `leaveStatus` TEXT, `appliedShiftIdForDay` TEXT, `actualDepartureTime` TEXT, `actualCheckInTime` TEXT, `actualCheckOutTime` TEXT, `actualArrivalTime` TEXT, `standardHoursScheduled` REAL NOT NULL, `otHoursScheduled` REAL NOT NULL, `sundayHoursScheduled` REAL NOT NULL, `nightHoursScheduled` REAL NOT NULL, `holidayHoursScheduled` REAL NOT NULL, `totalHoursScheduled` REAL NOT NULL, `lateMinutes` INTEGER NOT NULL, `earlyLeaveMinutes` INTEGER NOT NULL, `overtimeMinutes` INTEGER NOT NULL, `isHolidayWork` INTEGER NOT NULL, `isManualOverride` INTEGER NOT NULL, `isNightShiftDay` INTEGER NOT NULL, `lastCalculatedAt` TEXT NOT NULL, `calculationVersion` INTEGER NOT NULL, PRIMARY KEY(`date`))", "fields": [{"fieldPath": "date", "columnName": "date", "affinity": "TEXT", "notNull": true}, {"fieldPath": "complianceStatus", "columnName": "complianceStatus", "affinity": "TEXT", "notNull": true}, {"fieldPath": "leaveStatus", "columnName": "leaveStatus", "affinity": "TEXT", "notNull": false}, {"fieldPath": "appliedShiftIdForDay", "columnName": "appliedShiftIdForDay", "affinity": "TEXT", "notNull": false}, {"fieldPath": "actualDepartureTime", "columnName": "actualDepartureTime", "affinity": "TEXT", "notNull": false}, {"fieldPath": "actualCheckInTime", "columnName": "actualCheckInTime", "affinity": "TEXT", "notNull": false}, {"fieldPath": "actualCheckOutTime", "columnName": "actualCheckOutTime", "affinity": "TEXT", "notNull": false}, {"fieldPath": "actualArrivalTime", "columnName": "actualArrivalTime", "affinity": "TEXT", "notNull": false}, {"fieldPath": "standardHoursScheduled", "columnName": "standardHoursScheduled", "affinity": "REAL", "notNull": true}, {"fieldPath": "otHoursScheduled", "columnName": "otHoursScheduled", "affinity": "REAL", "notNull": true}, {"fieldPath": "sundayHoursScheduled", "columnName": "sundayHoursScheduled", "affinity": "REAL", "notNull": true}, {"fieldPath": "nightHoursScheduled", "columnName": "nightHoursScheduled", "affinity": "REAL", "notNull": true}, {"fieldPath": "holidayHoursScheduled", "columnName": "holidayHoursScheduled", "affinity": "REAL", "notNull": true}, {"fieldPath": "totalHoursScheduled", "columnName": "totalHoursScheduled", "affinity": "REAL", "notNull": true}, {"fieldPath": "lateMinutes", "columnName": "lateMinutes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "earlyLeaveMinutes", "columnName": "earlyLeaveMinutes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "overtimeMinutes", "columnName": "overtimeMinutes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isHolidayWork", "columnName": "isHolidayWork", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isManualOverride", "columnName": "isManualOverride", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isNightShiftDay", "columnName": "isNightShiftDay", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastCalculatedAt", "columnName": "lastCalculatedAt", "affinity": "TEXT", "notNull": true}, {"fieldPath": "calculationVersion", "columnName": "calculationVersion", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["date"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'afaefea0d2c6a0a87f7898fc4c998d35')"]}}
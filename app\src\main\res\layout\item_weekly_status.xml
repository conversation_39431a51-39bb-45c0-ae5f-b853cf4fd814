<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:layout_weight="1"
    android:layout_margin="2dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp"
        android:gravity="center">

        <!-- Day of Week -->
        <TextView
            android:id="@+id/text_view_day_of_week"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
            android:textColor="?attr/colorOnSurfaceVariant"
            tools:text="T2" />

        <!-- Date -->
        <TextView
            android:id="@+id/text_view_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="?attr/colorOnSurface"
            android:textStyle="bold"
            tools:text="02" />

        <!-- Status Icon -->
        <ImageView
            android:id="@+id/image_view_status_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginTop="4dp"
            android:contentDescription="Status icon"
            tools:src="@drawable/ic_check_circle"
            tools:tint="@color/green_500" />

        <!-- Status Text (Optional, for detailed view) -->
        <TextView
            android:id="@+id/text_view_status_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:visibility="gone"
            tools:text="✅" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

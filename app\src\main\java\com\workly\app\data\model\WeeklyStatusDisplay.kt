package com.workly.app.data.model

import java.time.LocalDate

/**
 * Enum cho các trạng thái hiển thị trong lưới tuần
 * Mapping từ ComplianceStatus và LeaveStatus thành các icon đơn giản
 */
enum class WeeklyDisplayStatus {
    // Trạng thái chính
    DU_CONG,           // ✅ - Đủ công (PERFECT_COMPLIANCE, COMPLETED_WITH_ISSUES)
    THIEU_LOG,         // ❗ - Đi làm nhưng thiếu chấm công (MISSING_CHECK_IN, MISSING_CHECK_OUT, MISSING_BOTH)
    DI_MUON,           // RV - Vào muộn (LATE_ARRIVAL)
    VE_SOM,            // RV - Ra sớm (EARLY_DEPARTURE) 
    DI_MUON_VE_SOM,    // RV - Cả muộn và sớm (LATE_AND_EARLY)
    
    // Trạng thái nghỉ
    NGHI_PHEP,         // 📩 P - Nghỉ phép (ANNUAL_LEAVE, PERSONAL_LEAVE, COMPENSATORY_LEAVE)
    NGHI_BENH,         // 🛌 B - Nghỉ bệnh (SICK_LEAVE)
    NGHI_LE,           // 🎌 H - Nghỉ lễ (PUBLIC_HOLIDAY)
    VANG_MAT,          // ❌ X - Vắng không lý do (UNPAID_LEAVE hoặc không có dữ liệu)
    
    // Trạng thái đặc biệt
    CHUA_CAP_NHAT,     // ❓ -- - Chưa có dữ liệu/Chưa cập nhật (NOT_STARTED, null)
    DANG_LAM,          // 🔄 - Đang trong ca (IN_PROGRESS)
    CHO_XEM_XET        // 🔍 - Chờ xem xét (PENDING_REVIEW, MANUAL_OVERRIDE)
}

/**
 * Data class cho item hiển thị trong lưới tuần
 */
data class WeeklyDisplayItem(
    val date: LocalDate,
    val dayOfWeek: String,        // "T2", "T3", "T4", "T5", "T6", "T7", "CN"
    val dayNumber: String,        // "01", "02", "03", etc.
    val status: WeeklyDisplayStatus,
    val isToday: Boolean = false,
    val isFuture: Boolean = false,
    val isClickable: Boolean = true,
    val originalComplianceStatus: ComplianceStatus? = null,
    val originalLeaveStatus: LeaveStatus? = null
)

/**
 * Extension functions cho WeeklyDisplayStatus
 */
fun WeeklyDisplayStatus.getDisplayIcon(): String {
    return when (this) {
        WeeklyDisplayStatus.DU_CONG -> "✅"
        WeeklyDisplayStatus.THIEU_LOG -> "❗"
        WeeklyDisplayStatus.DI_MUON -> "RV"
        WeeklyDisplayStatus.VE_SOM -> "RV"
        WeeklyDisplayStatus.DI_MUON_VE_SOM -> "RV"
        WeeklyDisplayStatus.NGHI_PHEP -> "📩"
        WeeklyDisplayStatus.NGHI_BENH -> "🛌"
        WeeklyDisplayStatus.NGHI_LE -> "🎌"
        WeeklyDisplayStatus.VANG_MAT -> "❌"
        WeeklyDisplayStatus.CHUA_CAP_NHAT -> "❓"
        WeeklyDisplayStatus.DANG_LAM -> "🔄"
        WeeklyDisplayStatus.CHO_XEM_XET -> "🔍"
    }
}

fun WeeklyDisplayStatus.getDisplayText(): String {
    return when (this) {
        WeeklyDisplayStatus.DU_CONG -> "Đủ công"
        WeeklyDisplayStatus.THIEU_LOG -> "Thiếu log"
        WeeklyDisplayStatus.DI_MUON -> "Đi muộn"
        WeeklyDisplayStatus.VE_SOM -> "Về sớm"
        WeeklyDisplayStatus.DI_MUON_VE_SOM -> "Muộn & Sớm"
        WeeklyDisplayStatus.NGHI_PHEP -> "Nghỉ phép"
        WeeklyDisplayStatus.NGHI_BENH -> "Nghỉ bệnh"
        WeeklyDisplayStatus.NGHI_LE -> "Nghỉ lễ"
        WeeklyDisplayStatus.VANG_MAT -> "Vắng mặt"
        WeeklyDisplayStatus.CHUA_CAP_NHAT -> "Chưa cập nhật"
        WeeklyDisplayStatus.DANG_LAM -> "Đang làm"
        WeeklyDisplayStatus.CHO_XEM_XET -> "Chờ xem xét"
    }
}

fun WeeklyDisplayStatus.getBackgroundColor(): Int {
    return when (this) {
        WeeklyDisplayStatus.DU_CONG -> 0xFF4CAF50.toInt() // Green
        WeeklyDisplayStatus.THIEU_LOG -> 0xFFFF9800.toInt() // Orange
        WeeklyDisplayStatus.DI_MUON -> 0xFFFF5722.toInt() // Deep Orange
        WeeklyDisplayStatus.VE_SOM -> 0xFFFF5722.toInt() // Deep Orange
        WeeklyDisplayStatus.DI_MUON_VE_SOM -> 0xFFF44336.toInt() // Red
        WeeklyDisplayStatus.NGHI_PHEP -> 0xFF2196F3.toInt() // Blue
        WeeklyDisplayStatus.NGHI_BENH -> 0xFFFFEB3B.toInt() // Yellow
        WeeklyDisplayStatus.NGHI_LE -> 0xFFE91E63.toInt() // Pink
        WeeklyDisplayStatus.VANG_MAT -> 0xFF9E9E9E.toInt() // Gray
        WeeklyDisplayStatus.CHUA_CAP_NHAT -> 0xFFEEEEEE.toInt() // Light Gray
        WeeklyDisplayStatus.DANG_LAM -> 0xFF00BCD4.toInt() // Cyan
        WeeklyDisplayStatus.CHO_XEM_XET -> 0xFF9C27B0.toInt() // Purple
    }
}

fun WeeklyDisplayStatus.getTextColor(): Int {
    return when (this) {
        WeeklyDisplayStatus.CHUA_CAP_NHAT -> 0xFF757575.toInt() // Dark Gray text
        WeeklyDisplayStatus.NGHI_BENH -> 0xFF333333.toInt() // Dark text for yellow background
        else -> 0xFFFFFFFF.toInt() // White text
    }
}

/**
 * Utility functions để convert từ ComplianceStatus và LeaveStatus
 */
fun mapToWeeklyDisplayStatus(
    complianceStatus: ComplianceStatus?,
    leaveStatus: LeaveStatus?
): WeeklyDisplayStatus {
    // Ưu tiên LeaveStatus trước
    leaveStatus?.let { leave ->
        return when (leave) {
            LeaveStatus.ANNUAL_LEAVE,
            LeaveStatus.PERSONAL_LEAVE,
            LeaveStatus.COMPENSATORY_LEAVE -> WeeklyDisplayStatus.NGHI_PHEP
            
            LeaveStatus.SICK_LEAVE -> WeeklyDisplayStatus.NGHI_BENH
            
            LeaveStatus.PUBLIC_HOLIDAY -> WeeklyDisplayStatus.NGHI_LE
            
            LeaveStatus.UNPAID_LEAVE -> WeeklyDisplayStatus.VANG_MAT
            
            LeaveStatus.MATERNITY_LEAVE,
            LeaveStatus.PATERNITY_LEAVE,
            LeaveStatus.BEREAVEMENT_LEAVE,
            LeaveStatus.BUSINESS_TRIP,
            LeaveStatus.TRAINING,
            LeaveStatus.OTHER -> WeeklyDisplayStatus.NGHI_PHEP // Treat as general leave
        }
    }
    
    // Nếu không có LeaveStatus, dựa vào ComplianceStatus
    return when (complianceStatus) {
        ComplianceStatus.PERFECT_COMPLIANCE,
        ComplianceStatus.COMPLETED_WITH_ISSUES -> WeeklyDisplayStatus.DU_CONG
        
        ComplianceStatus.LATE_ARRIVAL -> WeeklyDisplayStatus.DI_MUON
        ComplianceStatus.EARLY_DEPARTURE -> WeeklyDisplayStatus.VE_SOM
        ComplianceStatus.LATE_AND_EARLY -> WeeklyDisplayStatus.DI_MUON_VE_SOM
        
        ComplianceStatus.MISSING_CHECK_IN,
        ComplianceStatus.MISSING_CHECK_OUT,
        ComplianceStatus.MISSING_BOTH -> WeeklyDisplayStatus.THIEU_LOG
        
        ComplianceStatus.IN_PROGRESS -> WeeklyDisplayStatus.DANG_LAM
        
        ComplianceStatus.PENDING_REVIEW,
        ComplianceStatus.MANUAL_OVERRIDE,
        ComplianceStatus.SYSTEM_ERROR -> WeeklyDisplayStatus.CHO_XEM_XET
        
        ComplianceStatus.NOT_STARTED,
        null -> WeeklyDisplayStatus.CHUA_CAP_NHAT
    }
}

/**
 * Utility function để tạo danh sách các ngày trong tuần
 */
fun createWeeklyDisplayItems(
    startDate: LocalDate,
    dailyWorkStatuses: Map<LocalDate, DailyWorkStatus>,
    today: LocalDate = LocalDate.now()
): List<WeeklyDisplayItem> {
    val items = mutableListOf<WeeklyDisplayItem>()
    
    for (i in 0..6) { // 7 ngày trong tuần
        val date = startDate.plusDays(i.toLong())
        val dayOfWeek = when (date.dayOfWeek.value) {
            1 -> "T2"
            2 -> "T3"
            3 -> "T4"
            4 -> "T5"
            5 -> "T6"
            6 -> "T7"
            7 -> "CN"
            else -> "??"
        }
        
        val dayNumber = String.format("%02d", date.dayOfMonth)
        val workStatus = dailyWorkStatuses[date]
        val displayStatus = mapToWeeklyDisplayStatus(
            workStatus?.complianceStatus,
            workStatus?.leaveStatus
        )
        
        items.add(
            WeeklyDisplayItem(
                date = date,
                dayOfWeek = dayOfWeek,
                dayNumber = dayNumber,
                status = displayStatus,
                isToday = date == today,
                isFuture = date.isAfter(today),
                isClickable = true, // Có thể click để cập nhật trạng thái
                originalComplianceStatus = workStatus?.complianceStatus,
                originalLeaveStatus = workStatus?.leaveStatus
            )
        )
    }
    
    return items
}

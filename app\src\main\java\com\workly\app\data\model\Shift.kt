package com.workly.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize
import java.time.LocalDate
import java.time.LocalDateTime

@Parcelize
@Entity(tableName = "shifts")
data class Shift(
    @PrimaryKey
    val id: String,
    val name: String,
    val description: String? = null,

    // Thời gian ca làm việc (HH:MM format)
    val startTime: String,        // Giờ bắt đầu ca
    val endTime: String,          // Giờ kết thúc ca
    val departureTime: String,    // Giờ khuyến nghị đi làm
    val maxEndTime: String,       // Giờ kết thúc tối đa (cho nhắc nhở checkout)

    // Cấu hình ngày làm việc
    val daysApplied: List<String>, // ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    val workDays: List<Int> = emptyList(), // Backward compatibility

    // Cấu hình ca đêm
    val isNightShift: Boolean = false,
    val nightShiftStartDate: String? = null, // Ngày bắt đầu tính cho ca đêm

    // Cấu hình nghỉ giải lao
    val breakMinutes: Int = 0,
    val breakStartTime: String? = null,  // Giờ bắt đầu nghỉ (nếu cố định)
    val breakEndTime: String? = null,    // Giờ kết thúc nghỉ (nếu cố định)
    val isFlexibleBreak: Boolean = true, // Cho phép nghỉ linh hoạt

    // Cấu hình nhắc nhở
    val remindBeforeDeparture: Int = 30,  // Phút trước giờ đi
    val remindBeforeStart: Int = 15,      // Phút trước giờ bắt đầu ca
    val remindAfterEnd: Int = 15,         // Phút sau giờ kết thúc ca
    val enableDepartureReminder: Boolean = true,
    val enableStartReminder: Boolean = true,
    val enableEndReminder: Boolean = true,

    // Cấu hình chấm công
    val showPunch: Boolean = false,       // Hiển thị nút chấm công giữa ca
    val requireLocationCheck: Boolean = false, // Yêu cầu kiểm tra vị trí
    val autoCheckInRadius: Int = 100,     // Bán kính tự động chấm công (mét)

    // Cấu hình tính lương
    val hourlyRate: Double = 0.0,         // Lương theo giờ cơ bản
    val overtimeMultiplier: Double = 1.5, // Hệ số làm thêm giờ
    val nightShiftMultiplier: Double = 1.3, // Hệ số ca đêm
    val holidayMultiplier: Double = 2.0,  // Hệ số ngày lễ

    // Metadata
    val isActive: Boolean = true,
    val sortOrder: Int = 0,               // Thứ tự sắp xếp
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime
) : Parcelable

// Extension functions for day operations
fun Shift.isWorkDay(dayOfWeek: String): Boolean {
    return daysApplied.contains(dayOfWeek)
}

fun Shift.getWorkDaysDisplay(): String {
    return when {
        daysApplied.size == 7 -> "Tất cả các ngày"
        daysApplied.containsAll(listOf("Mon", "Tue", "Wed", "Thu", "Fri")) &&
        !daysApplied.contains("Sat") && !daysApplied.contains("Sun") -> "Thứ 2 - Thứ 6"
        else -> daysApplied.joinToString(", ") { day ->
            when (day) {
                "Mon" -> "T2"
                "Tue" -> "T3"
                "Wed" -> "T4"
                "Thu" -> "T5"
                "Fri" -> "T6"
                "Sat" -> "T7"
                "Sun" -> "CN"
                else -> day
            }
        }
    }
}

// Extension functions for time calculations
fun Shift.getTotalWorkMinutes(): Int {
    val start = parseTime(startTime)
    val end = parseTime(endTime)

    return if (isNightShift && end < start) {
        // Ca đêm: tính qua ngày
        (24 * 60 - start) + end - breakMinutes
    } else {
        // Ca thường
        end - start - breakMinutes
    }
}

fun Shift.getTotalWorkHours(): Double {
    return getTotalWorkMinutes() / 60.0
}

fun Shift.isCurrentlyActive(): Boolean {
    if (!isActive) return false

    val now = LocalDateTime.now()
    val currentDay = when (now.dayOfWeek.value) {
        1 -> "Mon"
        2 -> "Tue"
        3 -> "Wed"
        4 -> "Thu"
        5 -> "Fri"
        6 -> "Sat"
        7 -> "Sun"
        else -> ""
    }

    return isWorkDay(currentDay)
}

fun Shift.getNextWorkDate(): LocalDate? {
    val today = LocalDate.now()

    // Tìm ngày làm việc tiếp theo trong vòng 7 ngày
    for (i in 0..6) {
        val checkDate = today.plusDays(i.toLong())
        val dayOfWeek = when (checkDate.dayOfWeek.value) {
            1 -> "Mon"
            2 -> "Tue"
            3 -> "Wed"
            4 -> "Thu"
            5 -> "Fri"
            6 -> "Sat"
            7 -> "Sun"
            else -> ""
        }

        if (isWorkDay(dayOfWeek)) {
            return checkDate
        }
    }

    return null
}

// Helper function to parse time string to minutes
private fun parseTime(timeStr: String): Int {
    val parts = timeStr.split(":")
    return parts[0].toInt() * 60 + parts[1].toInt()
}

# Lưới Trạng Thái Tuần - Weekly Status Grid

## Tổng quan

Lưới Trạng Thái Tuần là một component UI hiển thị trạng thái chấm công của người dùng trong 7 ngày (từ Thứ 2 đến Chủ Nh<PERSON>) dưới dạng lưới 7 ô.

## Cấu trúc Files

```
app/src/main/java/com/workly/app/
├── data/model/
│   └── WeeklyStatusDisplay.kt          # Model và enum cho trạng thái hiển thị
├── ui/components/
│   └── WeeklyStatusGrid.kt             # Composable UI cho lưới
├── ui/home/
│   ├── HomeViewModel.kt                # Logic xử lý dữ liệu
│   ├── HomeUiState.kt                  # State cho UI
│   └── HomeFragmentExample.kt          # Ví dụ sử dụng
```

## Các Trạng Thái Hiển Thị

### Icon và Ý nghĩa

| Icon | Mã | Trạng thái | Mô tả |
|------|----|-----------|----|
| ✅ | DU_CONG | Đủ công | Hoàn thành đầy đủ chấm công |
| ❗ | THIEU_LOG | Thiếu log | Đi làm nhưng thiếu chấm công |
| RV | DI_MUON | Đi muộn | Vào làm muộn |
| RV | VE_SOM | Về sớm | Ra về sớm |
| RV | DI_MUON_VE_SOM | Muộn & Sớm | Cả vào muộn và ra sớm |
| 📩 | NGHI_PHEP | Nghỉ phép | Nghỉ phép có kế hoạch |
| 🛌 | NGHI_BENH | Nghỉ bệnh | Nghỉ bệnh |
| 🎌 | NGHI_LE | Nghỉ lễ | Nghỉ lễ/ngày tết |
| ❌ | VANG_MAT | Vắng mặt | Vắng không lý do |
| ❓ | CHUA_CAP_NHAT | Chưa cập nhật | Chưa có dữ liệu |
| 🔄 | DANG_LAM | Đang làm | Đang trong ca làm việc |
| 🔍 | CHO_XEM_XET | Chờ xem xét | Cần xem xét thủ công |

### Màu sắc

- **Xanh lá**: Trạng thái tốt (✅ Đủ công)
- **Cam**: Cảnh báo (❗ Thiếu log, RV Muộn/Sớm)
- **Đỏ**: Nghiêm trọng (❌ Vắng mặt, RV Muộn & Sớm)
- **Xanh dương**: Nghỉ có kế hoạch (📩 Nghỉ phép)
- **Vàng**: Nghỉ bệnh (🛌)
- **Hồng**: Nghỉ lễ (🎌)
- **Xám**: Chưa có dữ liệu (❓)

## Cách sử dụng

### 1. Trong ViewModel

```kotlin
class HomeViewModel @Inject constructor(
    private val dailyWorkStatusRepository: DailyWorkStatusRepository,
    // ...
) : ViewModel() {

    private fun loadWeeklyStatus() {
        viewModelScope.launch {
            val today = LocalDate.now()
            val startOfWeek = today.with(WeekFields.of(Locale.getDefault()).dayOfWeek(), 1)
            val endOfWeek = startOfWeek.plusDays(6)
            
            val dailyStatuses = dailyWorkStatusRepository.getStatusForDateRange(startOfWeek, endOfWeek)
            val statusMap = dailyStatuses.associateBy { it.date }
            
            val weeklyItems = createWeeklyDisplayItems(startOfWeek, statusMap, today)
            
            _uiState.value = _uiState.value.copy(
                weeklyStatus = weeklyItems,
                currentWeekRange = "${startOfWeek.dayOfMonth}/${startOfWeek.monthValue} - ${endOfWeek.dayOfMonth}/${endOfWeek.monthValue}"
            )
        }
    }
}
```

### 2. Trong Composable

```kotlin
@Composable
fun HomeScreen(viewModel: HomeViewModel = hiltViewModel()) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    var showUpdateDialog by remember { mutableStateOf(false) }
    var selectedItem by remember { mutableStateOf<WeeklyDisplayItem?>(null) }

    WeeklyStatusGrid(
        weeklyItems = uiState.weeklyStatus,
        onItemClick = { item ->
            viewModel.onWeeklyStatusItemClick(item)
        },
        onItemLongClick = { item ->
            if (item.isFuture) {
                selectedItem = item
                showUpdateDialog = true
            }
        }
    )

    // Dialog cập nhật trạng thái
    selectedItem?.let { item ->
        if (showUpdateDialog) {
            UpdateStatusDialog(
                item = item,
                onDismiss = { showUpdateDialog = false },
                onStatusUpdate = { newStatus ->
                    viewModel.updateWeeklyStatusItem(item, newStatus)
                    showUpdateDialog = false
                }
            )
        }
    }
}
```

## Tương tác người dùng

### Click thường
- **Ngày quá khứ/hôm nay**: Mở detail view để xem chi tiết chấm công
- **Ngày tương lai**: Có thể mở dialog cập nhật trạng thái

### Long click (nhấn giữ)
- **Ngày tương lai**: Mở dialog để cập nhật trạng thái nghỉ
- **Ngày quá khứ**: Có thể mở menu context với các tùy chọn

### Cập nhật trạng thái cho ngày tương lai
Người dùng có thể cập nhật các trạng thái sau cho ngày tương lai:
- 📩 Nghỉ phép (P)
- 🛌 Nghỉ bệnh (B) 
- 🎌 Nghỉ lễ (H)
- ❌ Vắng mặt (X)

## Logic mapping

### Từ ComplianceStatus + LeaveStatus → WeeklyDisplayStatus

```kotlin
fun mapToWeeklyDisplayStatus(
    complianceStatus: ComplianceStatus?,
    leaveStatus: LeaveStatus?
): WeeklyDisplayStatus {
    // Ưu tiên LeaveStatus trước
    leaveStatus?.let { leave ->
        return when (leave) {
            LeaveStatus.ANNUAL_LEAVE -> WeeklyDisplayStatus.NGHI_PHEP
            LeaveStatus.SICK_LEAVE -> WeeklyDisplayStatus.NGHI_BENH
            LeaveStatus.PUBLIC_HOLIDAY -> WeeklyDisplayStatus.NGHI_LE
            // ...
        }
    }
    
    // Nếu không có LeaveStatus, dựa vào ComplianceStatus
    return when (complianceStatus) {
        ComplianceStatus.PERFECT_COMPLIANCE -> WeeklyDisplayStatus.DU_CONG
        ComplianceStatus.LATE_ARRIVAL -> WeeklyDisplayStatus.DI_MUON
        ComplianceStatus.MISSING_BOTH -> WeeklyDisplayStatus.THIEU_LOG
        // ...
    }
}
```

## Tính năng đặc biệt

### Ngày hôm nay
- Có border màu xanh để highlight
- `isToday = true`

### Ngày tương lai  
- Hiển thị icon ❓ mặc định
- `isFuture = true`
- Có thể click để cập nhật trạng thái nghỉ
- Trạng thái nghỉ lễ có thể được hệ thống tự động cập nhật

### Tuần hiện tại
- Bắt đầu từ Thứ 2 (Monday)
- Hiển thị range: "01/12 - 07/12"

## Customization

### Thay đổi màu sắc
Chỉnh sửa trong `WeeklyStatusDisplay.kt`:

```kotlin
fun WeeklyDisplayStatus.getBackgroundColor(): Int {
    return when (this) {
        WeeklyDisplayStatus.DU_CONG -> 0xFF4CAF50.toInt() // Thay đổi màu
        // ...
    }
}
```

### Thay đổi icon
```kotlin
fun WeeklyDisplayStatus.getDisplayIcon(): String {
    return when (this) {
        WeeklyDisplayStatus.DU_CONG -> "✅" // Thay đổi icon
        // ...
    }
}
```

### Thêm trạng thái mới
1. Thêm vào enum `WeeklyDisplayStatus`
2. Cập nhật các extension functions
3. Cập nhật logic mapping
4. Cập nhật UI components

## Testing

Xem `HomeFragmentExample.kt` để có ví dụ về:
- Tạo dữ liệu mẫu
- Preview components
- Test các tương tác

## Performance

- Sử dụng `LazyColumn` cho danh sách lớn
- Cache dữ liệu weekly status
- Chỉ reload khi cần thiết
- Sử dụng `collectAsStateWithLifecycle()` để tránh memory leaks

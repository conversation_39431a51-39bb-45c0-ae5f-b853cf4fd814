package com.workly.app.di

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.workly.app.data.repository.AttendanceRepository
import com.workly.app.data.repository.AttendanceRepositoryImpl
import com.workly.app.data.repository.DailyWorkStatusRepository
import com.workly.app.data.repository.DailyWorkStatusRepositoryImpl
import com.workly.app.data.repository.NoteRepository
import com.workly.app.data.repository.NoteRepositoryImpl
import com.workly.app.data.repository.SettingsRepository
import com.workly.app.data.repository.SettingsRepositoryImpl
import com.workly.app.data.repository.ShiftRepository
import com.workly.app.data.repository.ShiftRepositoryImpl
import com.workly.app.data.repository.WorkCalculationRepository
import com.workly.app.data.repository.WorkCalculationRepositoryImpl
import com.workly.app.services.LocationService
import com.workly.app.services.LocationServiceImpl
import com.workly.app.services.NotificationService
import com.workly.app.services.NotificationServiceImpl
import com.workly.app.services.ReminderSchedulingService
import com.workly.app.services.ReminderSchedulingServiceImpl
import com.workly.app.services.ShiftRotationService
import com.workly.app.services.ShiftRotationServiceImpl
import com.workly.app.services.WeatherService
import com.workly.app.services.WeatherServiceImpl
import com.workly.app.services.WorkCalculationService
import com.workly.app.services.WorkCalculationServiceImpl
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class AppModule {

    @Binds
    abstract fun bindShiftRepository(
        shiftRepositoryImpl: ShiftRepositoryImpl
    ): ShiftRepository

    @Binds
    abstract fun bindAttendanceRepository(
        attendanceRepositoryImpl: AttendanceRepositoryImpl
    ): AttendanceRepository

    @Binds
    abstract fun bindNoteRepository(
        noteRepositoryImpl: NoteRepositoryImpl
    ): NoteRepository

    @Binds
    abstract fun bindSettingsRepository(
        settingsRepositoryImpl: SettingsRepositoryImpl
    ): SettingsRepository

    @Binds
    abstract fun bindDailyWorkStatusRepository(
        dailyWorkStatusRepositoryImpl: DailyWorkStatusRepositoryImpl
    ): DailyWorkStatusRepository

    @Binds
    abstract fun bindWorkCalculationRepository(
        workCalculationRepositoryImpl: WorkCalculationRepositoryImpl
    ): WorkCalculationRepository

    @Binds
    abstract fun bindLocationService(
        locationServiceImpl: LocationServiceImpl
    ): LocationService

    @Binds
    abstract fun bindNotificationService(
        notificationServiceImpl: NotificationServiceImpl
    ): NotificationService

    @Binds
    abstract fun bindWeatherService(
        weatherServiceImpl: WeatherServiceImpl
    ): WeatherService

    @Binds
    abstract fun bindReminderSchedulingService(
        reminderSchedulingServiceImpl: ReminderSchedulingServiceImpl
    ): ReminderSchedulingService

    @Binds
    abstract fun bindShiftRotationService(
        shiftRotationServiceImpl: ShiftRotationServiceImpl
    ): ShiftRotationService

    @Binds
    abstract fun bindWorkCalculationService(
        workCalculationServiceImpl: WorkCalculationServiceImpl
    ): WorkCalculationService

    companion object {
        @Provides
        @Singleton
        fun provideSharedPreferences(@ApplicationContext context: Context): SharedPreferences {
            return context.getSharedPreferences("workly_prefs", Context.MODE_PRIVATE)
        }

        @Provides
        @Singleton
        fun provideGson(): Gson {
            return GsonBuilder()
                .setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
                .create()
        }
    }
}

package com.workly.app.services

import com.workly.app.data.model.*
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Service quản lý hệ thống xoay ca tự động
 */
interface ShiftRotationService {
    
    /**
     * Kiểm tra và thực hiện xoay ca nếu đến hạn
     */
    suspend fun checkAndRotateShift(): ShiftRotationResult
    
    /**
     * Tính toán ca tiếp theo trong chu kỳ xoay
     */
    suspend fun calculateNextShift(
        currentShiftId: String,
        rotationConfig: RotationConfig
    ): String?
    
    /**
     * Kiểm tra xem có đến hạn xoay ca không
     */
    suspend fun isRotationDue(rotationConfig: RotationConfig): Boolean
    
    /**
     * Lấy thông tin ca hiện tại và ca tiếp theo
     */
    suspend fun getCurrentRotationInfo(): RotationInfo?
    
    /**
     * Cập nhật cấu hình xoay ca
     */
    suspend fun updateRotationConfig(config: RotationConfig)
    
    /**
     * Bật/tắt chế độ xoay ca
     */
    suspend fun setRotationEnabled(enabled: Boolean)
    
    /**
     * Đặt lại chu kỳ xoay ca
     */
    suspend fun resetRotationCycle()
    
    /**
     * Xoay ca thủ công
     */
    suspend fun manualRotateShift(): ShiftRotationResult
    
    /**
     * Lấy lịch sử xoay ca
     */
    suspend fun getRotationHistory(limit: Int = 10): List<RotationHistoryItem>
    
    /**
     * Dự đoán lịch xoay ca trong tương lai
     */
    suspend fun predictRotationSchedule(days: Int = 30): List<RotationPrediction>
    
    /**
     * Gửi thông báo nhắc nhở xoay ca
     */
    suspend fun sendRotationReminder(nextShift: Shift, rotationDate: LocalDate)
}

/**
 * Kết quả xoay ca
 */
data class ShiftRotationResult(
    val success: Boolean,
    val previousShiftId: String?,
    val newShiftId: String?,
    val rotationDate: LocalDate,
    val rotationType: RotationType,
    val message: String,
    val nextRotationDate: LocalDate? = null,
    val errors: List<String> = emptyList()
)

/**
 * Thông tin xoay ca hiện tại
 */
data class RotationInfo(
    val currentShift: Shift,
    val nextShift: Shift?,
    val rotationConfig: RotationConfig,
    val isRotationEnabled: Boolean,
    val daysSinceLastRotation: Int,
    val daysUntilNextRotation: Int,
    val rotationProgress: Double, // 0.0 - 1.0
    val lastRotationDate: LocalDate?,
    val nextRotationDate: LocalDate?
)

/**
 * Loại xoay ca
 */
enum class RotationType {
    AUTOMATIC,    // Tự động theo lịch
    MANUAL,       // Thủ công
    FORCED,       // Bắt buộc (do lỗi hoặc yêu cầu đặc biệt)
    SCHEDULED     // Theo lịch trình định sẵn
}

/**
 * Item lịch sử xoay ca
 */
data class RotationHistoryItem(
    val id: String,
    val rotationDate: LocalDate,
    val fromShiftId: String,
    val toShiftId: String,
    val fromShiftName: String,
    val toShiftName: String,
    val rotationType: RotationType,
    val reason: String? = null,
    val createdAt: LocalDateTime
)

/**
 * Dự đoán xoay ca
 */
data class RotationPrediction(
    val date: LocalDate,
    val shiftId: String,
    val shiftName: String,
    val rotationIndex: Int,
    val isRotationDay: Boolean,
    val confidence: Double // 0.0 - 1.0
)

/**
 * Cấu hình nâng cao cho xoay ca
 */
data class AdvancedRotationConfig(
    val enableWeekendSkip: Boolean = false,        // Bỏ qua cuối tuần
    val enableHolidaySkip: Boolean = false,        // Bỏ qua ngày lễ
    val customSkipDates: List<LocalDate> = emptyList(), // Ngày bỏ qua tùy chỉnh
    val rotationStartTime: String = "00:00",       // Giờ bắt đầu xoay ca trong ngày
    val enableNotification: Boolean = true,        // Bật thông báo
    val notificationAdvanceDays: Int = 1,          // Thông báo trước bao nhiêu ngày
    val enableAutoApply: Boolean = true,           // Tự động áp dụng
    val requireConfirmation: Boolean = false,      // Yêu cầu xác nhận
    val backupShiftId: String? = null,             // Ca dự phòng
    val maxConsecutiveDays: Int = 0,               // Số ngày tối đa liên tiếp (0 = không giới hạn)
    val minRestDaysBetween: Int = 0                // Số ngày nghỉ tối thiểu giữa các ca
)

/**
 * Trạng thái xoay ca
 */
enum class RotationStatus {
    ACTIVE,       // Đang hoạt động
    PAUSED,       // Tạm dừng
    DISABLED,     // Vô hiệu hóa
    ERROR,        // Lỗi
    PENDING       // Chờ xử lý
}

/**
 * Context cho việc tính toán xoay ca
 */
data class RotationContext(
    val currentDate: LocalDate,
    val currentShift: Shift?,
    val availableShifts: List<Shift>,
    val rotationConfig: RotationConfig,
    val advancedConfig: AdvancedRotationConfig,
    val userSettings: UserSettings,
    val rotationHistory: List<RotationHistoryItem>,
    val holidays: List<LocalDate> = emptyList()
)

/**
 * Chiến lược xoay ca
 */
interface RotationStrategy {
    suspend fun calculateNextRotation(context: RotationContext): RotationCalculation
    suspend fun isRotationDue(context: RotationContext): Boolean
    suspend fun validateRotation(context: RotationContext): RotationValidation
}

/**
 * Kết quả tính toán xoay ca
 */
data class RotationCalculation(
    val nextShiftId: String,
    val rotationDate: LocalDate,
    val confidence: Double,
    val reason: String,
    val warnings: List<String> = emptyList()
)

/**
 * Kết quả validation xoay ca
 */
data class RotationValidation(
    val isValid: Boolean,
    val errors: List<String> = emptyList(),
    val warnings: List<String> = emptyList(),
    val suggestions: List<String> = emptyList()
)

/**
 * Chiến lược xoay ca mặc định
 */
class DefaultRotationStrategy : RotationStrategy {
    
    override suspend fun calculateNextRotation(context: RotationContext): RotationCalculation {
        val config = context.rotationConfig
        val currentIndex = config.currentRotationIndex
        val shifts = config.rotationShifts
        
        if (shifts.isEmpty()) {
            throw IllegalStateException("No shifts configured for rotation")
        }
        
        val nextIndex = (currentIndex + 1) % shifts.size
        val nextShiftId = shifts[nextIndex]
        
        return RotationCalculation(
            nextShiftId = nextShiftId,
            rotationDate = calculateNextRotationDate(context),
            confidence = 1.0,
            reason = "Sequential rotation based on configured frequency"
        )
    }
    
    override suspend fun isRotationDue(context: RotationContext): Boolean {
        val config = context.rotationConfig
        val lastAppliedDate = config.rotationLastAppliedDate?.let { LocalDate.parse(it) }
            ?: return true // First rotation
        
        val daysSinceLastRotation = java.time.temporal.ChronoUnit.DAYS.between(
            lastAppliedDate, 
            context.currentDate
        ).toInt()
        
        val rotationIntervalDays = when (config.rotationFrequency) {
            RotationFrequency.DAILY -> 1
            RotationFrequency.WEEKLY -> 7
            RotationFrequency.BIWEEKLY -> 14
            RotationFrequency.TRIWEEKLY -> 21
            RotationFrequency.MONTHLY -> 30
            RotationFrequency.CUSTOM -> 7 // Default
        }
        
        return daysSinceLastRotation >= rotationIntervalDays
    }
    
    override suspend fun validateRotation(context: RotationContext): RotationValidation {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()
        
        // Validate shifts exist
        if (context.rotationConfig.rotationShifts.isEmpty()) {
            errors.add("No shifts configured for rotation")
        }
        
        // Validate current shift
        if (context.currentShift == null) {
            warnings.add("No current shift set")
        }
        
        // Check for conflicts
        val advancedConfig = context.advancedConfig
        if (advancedConfig.enableWeekendSkip && context.currentDate.dayOfWeek.value >= 6) {
            warnings.add("Rotation scheduled on weekend (will be skipped)")
        }
        
        return RotationValidation(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings
        )
    }
    
    private fun calculateNextRotationDate(context: RotationContext): LocalDate {
        val config = context.rotationConfig
        val lastAppliedDate = config.rotationLastAppliedDate?.let { LocalDate.parse(it) }
            ?: context.currentDate
        
        return when (config.rotationFrequency) {
            RotationFrequency.DAILY -> lastAppliedDate.plusDays(1)
            RotationFrequency.WEEKLY -> lastAppliedDate.plusWeeks(1)
            RotationFrequency.BIWEEKLY -> lastAppliedDate.plusWeeks(2)
            RotationFrequency.TRIWEEKLY -> lastAppliedDate.plusWeeks(3)
            RotationFrequency.MONTHLY -> lastAppliedDate.plusMonths(1)
            RotationFrequency.CUSTOM -> lastAppliedDate.plusDays(7) // Default
        }
    }
}

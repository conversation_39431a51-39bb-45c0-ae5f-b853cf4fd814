package com.workly.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize
import java.time.LocalDateTime

@Parcelize
@Entity(tableName = "notes")
data class Note(
    @PrimaryKey
    val id: String,
    val title: String,
    val content: String,

    // Priority and display settings (Logic "Slot Đảm bảo + Ưu tiên")
    val isPriority: Boolean = false, // Ưu tiên (hiển thị với ⭐)
    val isGuaranteedSlot: Boolean = false, // Slot đảm bảo (luôn được hiển thị)
    val isPinned: Boolean = false, // Ghim (luôn ở đầu danh sách)

    // Reminder settings
    val reminderDateTime: LocalDateTime? = null,
    val enableNotifications: Boolean = true, // Cho phép hiển thị thông báo/nhắc nhở
    val notificationAdvanceMinutes: Int = 15, // Nhắc trước bao nhiêu phút
    val remindForShifts: Bo<PERSON>an = false, // Nhắc nhở theo ca làm việc

    // Associations
    val associatedShiftIds: List<String> = emptyList(), // Ca làm việc liên quan
    val associatedDates: List<String> = emptyList(), // Ngày cụ thể (yyyy-MM-dd)
    val tags: List<String> = emptyList(),

    // Status and visibility
    val isCompleted: Boolean = false,
    val isArchived: Boolean = false,
    val isHiddenFromHome: Boolean = false, // Tạm ẩn khỏi trang chủ
    val snoozeUntil: LocalDateTime? = null, // Báo lại sau thời gian này

    // Repeat settings
    val repeatType: NoteRepeatType = NoteRepeatType.NONE,
    val repeatInterval: Int = 1, // Khoảng cách lặp lại
    val repeatEndDate: LocalDateTime? = null,
    val repeatDaysOfWeek: List<Int> = emptyList(), // 1-7 (Monday-Sunday)

    // Display customization
    val category: NoteCategory = NoteCategory.GENERAL,
    val color: String? = null, // Hex color code
    val icon: String? = null, // Emoji icon

    // Metadata
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val lastViewedAt: LocalDateTime? = null,
    val viewCount: Int = 0,
    val sortOrder: Int = 0, // Thứ tự sắp xếp tùy chỉnh
    val location: String? = null,
    val attachments: List<String> = emptyList() // File paths
) : Parcelable

/**
 * Loại lặp lại ghi chú
 */
enum class NoteRepeatType {
    NONE,           // Không lặp lại
    DAILY,          // Hàng ngày
    WEEKLY,         // Hàng tuần
    MONTHLY,        // Hàng tháng
    YEARLY,         // Hàng năm
    WORKDAYS,       // Các ngày làm việc
    CUSTOM          // Tùy chỉnh
}

/**
 * Danh mục ghi chú
 */
enum class NoteCategory {
    GENERAL,        // Chung
    WORK,           // Công việc
    PERSONAL,       // Cá nhân
    MEETING,        // Họp
    DEADLINE,       // Deadline
    REMINDER,       // Nhắc nhở
    SHOPPING,       // Mua sắm
    HEALTH,         // Sức khỏe
    FINANCE,        // Tài chính
    TRAVEL,         // Du lịch
    LEARNING,       // Học tập
    OTHER           // Khác
}

/**
 * Mức độ ưu tiên hiển thị (cho logic Slot Đảm bảo + Ưu tiên)
 */
enum class DisplayPriority {
    GUARANTEED_SLOT,    // Slot đảm bảo (luôn hiển thị)
    HIGH_PRIORITY,      // Ưu tiên cao (isPriority = true)
    PINNED,            // Ghim (isPinned = true)
    NORMAL,            // Bình thường
    LOW                // Thấp (có thể bị ẩn khi hết slot)
}

// Extension functions
fun Note.isActive(): Boolean {
    val now = LocalDateTime.now()
    return !isCompleted && !isArchived && !isHiddenFromHome &&
           (snoozeUntil == null || snoozeUntil.isBefore(now))
}

fun Note.hasReminder(): Boolean {
    return reminderDateTime != null && enableNotifications
}

fun Note.isOverdue(): Boolean {
    return reminderDateTime?.let { it.isBefore(LocalDateTime.now()) } ?: false
}

fun Note.getDisplayTitle(): String {
    val prefix = when {
        isPinned -> "📌 "
        isPriority -> "⭐ "
        else -> ""
    }
    return "$prefix$title"
}

fun Note.getDisplayPriority(): DisplayPriority {
    return when {
        isGuaranteedSlot -> DisplayPriority.GUARANTEED_SLOT
        isPriority -> DisplayPriority.HIGH_PRIORITY
        isPinned -> DisplayPriority.PINNED
        else -> DisplayPriority.NORMAL
    }
}

fun Note.shouldShowOnHomeScreen(maxSlots: Int, currentSlotIndex: Int): Boolean {
    return when (getDisplayPriority()) {
        DisplayPriority.GUARANTEED_SLOT -> true // Luôn hiển thị
        DisplayPriority.HIGH_PRIORITY -> currentSlotIndex < maxSlots // Ưu tiên trong giới hạn slot
        DisplayPriority.PINNED -> currentSlotIndex < maxSlots // Ghim trong giới hạn slot
        DisplayPriority.NORMAL -> currentSlotIndex < maxSlots // Bình thường trong giới hạn slot
        DisplayPriority.LOW -> false // Không hiển thị nếu hết slot
    }
}

fun Note.getIcon(): String {
    return icon ?: when (category) {
        NoteCategory.WORK -> "💼"
        NoteCategory.PERSONAL -> "👤"
        NoteCategory.MEETING -> "🤝"
        NoteCategory.DEADLINE -> "⏰"
        NoteCategory.REMINDER -> "🔔"
        NoteCategory.SHOPPING -> "🛒"
        NoteCategory.HEALTH -> "🏥"
        NoteCategory.FINANCE -> "💰"
        NoteCategory.TRAVEL -> "✈️"
        NoteCategory.LEARNING -> "📚"
        else -> "📝"
    }
}

fun Note.getCategoryDisplayName(): String {
    return when (category) {
        NoteCategory.GENERAL -> "Chung"
        NoteCategory.WORK -> "Công việc"
        NoteCategory.PERSONAL -> "Cá nhân"
        NoteCategory.MEETING -> "Họp"
        NoteCategory.DEADLINE -> "Deadline"
        NoteCategory.REMINDER -> "Nhắc nhở"
        NoteCategory.SHOPPING -> "Mua sắm"
        NoteCategory.HEALTH -> "Sức khỏe"
        NoteCategory.FINANCE -> "Tài chính"
        NoteCategory.TRAVEL -> "Du lịch"
        NoteCategory.LEARNING -> "Học tập"
        NoteCategory.OTHER -> "Khác"
    }
}

fun Note.isRelevantForShift(shiftId: String): Boolean {
    return associatedShiftIds.contains(shiftId) || associatedShiftIds.isEmpty()
}

fun Note.isRelevantForDate(date: java.time.LocalDate): Boolean {
    val dateStr = date.toString()
    return associatedDates.contains(dateStr) || associatedDates.isEmpty()
}

fun Note.getNextReminderTime(): LocalDateTime? {
    if (!hasReminder()) return null

    val baseTime = reminderDateTime ?: return null
    val now = LocalDateTime.now()

    if (repeatType == NoteRepeatType.NONE) {
        return if (baseTime.isAfter(now)) baseTime else null
    }

    // Calculate next occurrence for repeating notes
    var nextTime = baseTime
    while (nextTime.isBefore(now)) {
        nextTime = when (repeatType) {
            NoteRepeatType.DAILY -> nextTime.plusDays(repeatInterval.toLong())
            NoteRepeatType.WEEKLY -> nextTime.plusWeeks(repeatInterval.toLong())
            NoteRepeatType.MONTHLY -> nextTime.plusMonths(repeatInterval.toLong())
            NoteRepeatType.YEARLY -> nextTime.plusYears(repeatInterval.toLong())
            NoteRepeatType.WORKDAYS -> {
                // Skip weekends
                var temp = nextTime.plusDays(1)
                while (temp.dayOfWeek.value > 5) { // Saturday = 6, Sunday = 7
                    temp = temp.plusDays(1)
                }
                temp
            }
            else -> return null
        }

        // Check if we've passed the end date
        if (repeatEndDate != null && nextTime.isAfter(repeatEndDate)) {
            return null
        }
    }

    return nextTime
}

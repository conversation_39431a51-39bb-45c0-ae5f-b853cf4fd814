package com.workly.app.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.workly.app.data.model.MultiFunctionButtonInfo

/**
 * Placeholder Multi-Function Button
 */
@Composable
fun MultiFunctionButton(
    buttonInfo: MultiFunctionButtonInfo,
    onButtonClick: () -> Unit,
    onResetClick: () -> Unit = {},
    onPunchClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.padding(16.dp)) {
        Button(
            onClick = onButtonClick,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(text = buttonInfo.text)
        }
    }
}

@Composable
fun ButtonActionHistoryDisplay(
    history: List<com.workly.app.data.model.ButtonActionHistory>,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "History: ${history.size} items",
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Composable
fun WeatherWarningBanner(
    warning: com.workly.app.data.model.WeatherWarning?,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    if (warning != null) {
        Card(modifier = modifier.fillMaxWidth().padding(16.dp)) {
            Text(
                text = "Weather Warning: ${warning.description}",
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

@Composable
fun WorkNotesSection(
    notes: List<com.workly.app.data.model.Note>,
    hasMoreNotes: Boolean = false,
    onAddNote: () -> Unit,
    onEditNote: (com.workly.app.data.model.Note) -> Unit,
    onDeleteNote: (com.workly.app.data.model.Note) -> Unit,
    onViewAllNotes: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(text = "Work Notes (${notes.size})")
            Button(onClick = onAddNote) {
                Text("Add Note")
            }
        }
    }
}

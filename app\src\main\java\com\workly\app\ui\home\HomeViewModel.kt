package com.workly.app.ui.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.data.model.*
import com.workly.app.data.repository.AttendanceRepository
import com.workly.app.data.repository.DailyWorkStatusRepository
import com.workly.app.data.repository.NoteRepository
import com.workly.app.data.repository.SettingsRepository
import com.workly.app.services.AttendanceButtonState
import com.workly.app.services.LocationService
import com.workly.app.services.NotificationService
import com.workly.app.services.WeatherService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.temporal.WeekFields
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val attendanceRepository: AttendanceRepository,
    private val dailyWorkStatusRepository: DailyWorkStatusRepository,
    private val noteRepository: NoteRepository,
    private val settingsRepository: SettingsRepository,
    private val locationService: LocationService,
    private val notificationService: NotificationService,
    private val weatherService: WeatherService
) : ViewModel() {

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    private val _attendanceHistory = MutableStateFlow<List<AttendanceLog>>(emptyList())
    val attendanceHistory: StateFlow<List<AttendanceLog>> = _attendanceHistory.asStateFlow()

    private val _activeNotes = MutableStateFlow<List<Note>>(emptyList())
    val activeNotes: StateFlow<List<Note>> = _activeNotes.asStateFlow()

    private val _weatherData = MutableStateFlow<WeatherData?>(null)
    val weatherData: StateFlow<WeatherData?> = _weatherData.asStateFlow()

    init {
        loadData()
    }

    private fun loadData() {
        loadAttendanceHistory()
        loadActiveNotes()
        loadWeatherData()
        loadWeeklyStatus()
        loadButtonState()
        loadButtonHistory()
    }

    private fun loadAttendanceHistory() {
        viewModelScope.launch {
            val fromDate = LocalDateTime.now().minusDays(7)
            attendanceRepository.getRecentLogsFlow(fromDate, 10).collectLatest { logs ->
                _attendanceHistory.value = logs
            }
        }
    }

    private fun loadActiveNotes() {
        viewModelScope.launch {
            noteRepository.getActiveNotesFlow().collectLatest { notes ->
                _activeNotes.value = notes.take(3) // Show only first 3 notes
            }
        }
    }

    private fun loadWeatherData() {
        viewModelScope.launch {
            try {
                val weather = weatherService.getCurrentWeather()
                _weatherData.value = weather ?: getSampleWeatherData()
            } catch (e: Exception) {
                // Use sample data if weather service fails
                _weatherData.value = getSampleWeatherData()
            }
        }
    }

    private fun getSampleWeatherData(): WeatherData {
        return com.workly.app.data.SampleDataProvider.getSampleWeatherData()
    }

    private fun loadWeeklyStatus() {
        viewModelScope.launch {
            try {
                val today = LocalDate.now()
                val weekFields = WeekFields.of(Locale.getDefault())
                val startOfWeek = today.with(weekFields.dayOfWeek(), 1) // Thứ 2

                // Lấy dữ liệu DailyWorkStatus cho tuần này
                val endOfWeek = startOfWeek.plusDays(6)
                val dailyStatuses = dailyWorkStatusRepository.getStatusForDateRange(startOfWeek, endOfWeek)
                val statusMap = dailyStatuses.associateBy { it.date }

                // Tạo WeeklyDisplayItems
                val weeklyItems = createWeeklyDisplayItems(startOfWeek, statusMap, today)

                // Tạo text hiển thị khoảng tuần
                val weekRange = "${startOfWeek.dayOfMonth}/${startOfWeek.monthValue} - ${endOfWeek.dayOfMonth}/${endOfWeek.monthValue}"

                // Cập nhật UI state
                _uiState.value = _uiState.value.copy(
                    weeklyStatus = weeklyItems,
                    currentWeekRange = weekRange
                )

            } catch (e: Exception) {
                // Handle error - có thể tạo dữ liệu mẫu
                _uiState.value = _uiState.value.copy(
                    weeklyStatus = createSampleWeeklyStatus(),
                    currentWeekRange = "Tuần hiện tại"
                )
            }
        }
    }

    private fun createSampleWeeklyStatus(): List<WeeklyDisplayItem> {
        val today = LocalDate.now()
        val startOfWeek = today.minusDays(today.dayOfWeek.value - 1L) // Thứ 2
        return createWeeklyDisplayItems(startOfWeek, emptyMap(), today)
    }

    private fun loadButtonState() {
        viewModelScope.launch {
            try {
                val today = LocalDate.now()
                val todayLogs = attendanceRepository.getLogsForDate(today)
                val settings = settingsRepository.getUserSettings().first()

                // Determine current state based on existing logs
                val currentState = determineButtonStateFromLogs(todayLogs)
                val buttonMode = if (settings.enableSimpleMode) ButtonMode.SIMPLE else ButtonMode.FULL

                val buttonInfo = MultiFunctionButtonInfo(
                    state = currentState,
                    mode = buttonMode,
                    text = currentState.getDisplayText(buttonMode),
                    icon = currentState.getDisplayIcon(),
                    color = currentState.getColor(),
                    isEnabled = currentState.isEnabled(),
                    isVisible = currentState != MultiFunctionButtonState.HIDDEN,
                    showResetButton = todayLogs.isNotEmpty(),
                    showPunchButton = shouldShowPunchButton(currentState)
                )

                _uiState.value = _uiState.value.copy(
                    buttonInfo = buttonInfo
                )

            } catch (e: Exception) {
                // Use default state
                val defaultButtonInfo = MultiFunctionButtonInfo(
                    state = MultiFunctionButtonState.DI_LAM,
                    mode = ButtonMode.FULL,
                    text = "Đi Làm",
                    icon = "🚶‍♂️",
                    color = 0xFF2196F3.toInt(),
                    isEnabled = true,
                    isVisible = true
                )

                _uiState.value = _uiState.value.copy(
                    buttonInfo = defaultButtonInfo
                )
            }
        }
    }

    private fun loadButtonHistory() {
        viewModelScope.launch {
            try {
                val today = LocalDate.now()
                val todayLogs = attendanceRepository.getLogsForDate(today)

                val history = todayLogs.map { log ->
                    ButtonActionHistory(
                        id = log.id,
                        state = mapAttendanceTypeToButtonState(log.type),
                        timestamp = log.time,
                        attendanceType = log.type,
                        location = log.location,
                        isManual = log.isManualEntry
                    )
                }.sortedBy { it.timestamp }

                _uiState.value = _uiState.value.copy(
                    buttonActionHistory = history
                )

            } catch (e: Exception) {
                // Handle error silently for history
            }
        }
    }

    private fun determineButtonStateFromLogs(logs: List<AttendanceLog>): MultiFunctionButtonState {
        if (logs.isEmpty()) {
            return MultiFunctionButtonState.DI_LAM
        }

        val sortedLogs = logs.sortedBy { it.time }
        val lastLog = sortedLogs.last()

        return when (lastLog.type) {
            AttendanceType.DEPARTURE -> MultiFunctionButtonState.CHAM_CONG_VAO
            AttendanceType.CHECK_IN -> MultiFunctionButtonState.CHAM_CONG_RA
            AttendanceType.CHECK_OUT -> MultiFunctionButtonState.HOAN_TAT
            AttendanceType.ARRIVAL -> MultiFunctionButtonState.DA_HOAN_TAT
            else -> MultiFunctionButtonState.DI_LAM
        }
    }

    private fun mapAttendanceTypeToButtonState(type: AttendanceType): MultiFunctionButtonState {
        return when (type) {
            AttendanceType.DEPARTURE -> MultiFunctionButtonState.DI_LAM
            AttendanceType.CHECK_IN -> MultiFunctionButtonState.CHAM_CONG_VAO
            AttendanceType.CHECK_OUT -> MultiFunctionButtonState.CHAM_CONG_RA
            AttendanceType.ARRIVAL -> MultiFunctionButtonState.HOAN_TAT
            AttendanceType.PUNCH -> MultiFunctionButtonState.CHAM_CONG_VAO // Placeholder
            else -> MultiFunctionButtonState.DI_LAM
        }
    }

    fun handleMultiFunctionButtonClick() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                // Handle button click based on current state
                val currentState = determineButtonState()
                when (currentState) {
                    AttendanceButtonState.GO_WORK -> handleGoWork()
                    AttendanceButtonState.CHECK_IN -> handleCheckIn()
                    AttendanceButtonState.CHECK_OUT -> handleCheckOut()
                    AttendanceButtonState.COMPLETED -> handleCompletedDay()
                    else -> {}
                }
            } catch (e: Exception) {
                // Handle error
            } finally {
                _uiState.value = _uiState.value.copy(isLoading = false)
            }
        }
    }

    fun snoozeNote(note: Note) {
        viewModelScope.launch {
            val snoozedNote = note.copy(
                snoozeUntil = LocalDateTime.now().plusHours(1)
            )
            noteRepository.updateNote(snoozedNote)
        }
    }

    private fun determineButtonState(): AttendanceButtonState {
        // Logic to determine current button state
        // This would check current time, active shift, recent attendance logs, etc.
        return AttendanceButtonState.GO_WORK // Placeholder
    }

    private fun getButtonText(state: AttendanceButtonState): String {
        return when (state) {
            AttendanceButtonState.GO_WORK -> "Đi làm"
            AttendanceButtonState.CHECK_IN -> "Vào làm"
            AttendanceButtonState.CHECK_OUT -> "Ra về"
            AttendanceButtonState.COMPLETED -> "Hoàn thành"
            else -> "Chấm công"
        }
    }

    private fun getCurrentStatusText(): String {
        // Logic to get current work status text
        return "Sẵn sàng làm việc" // Placeholder
    }

    private suspend fun handleGoWork() {
        // Implementation for "Go Work" action
    }

    private suspend fun handleCheckIn() {
        // Implementation for "Check In" action
    }

    private suspend fun handleCheckOut() {
        // Implementation for "Check Out" action
    }

    private suspend fun handleCompletedDay() {
        // Implementation for "Completed Day" action
    }

    // Weekly Status Grid Actions
    fun onWeeklyStatusItemClick(item: WeeklyDisplayItem) {
        viewModelScope.launch {
            // Xử lý click vào item - có thể mở detail view hoặc edit dialog
            // Tùy thuộc vào trạng thái của item
            if (item.isFuture) {
                // Ngày tương lai - có thể cập nhật trạng thái nghỉ
                // Logic sẽ được xử lý trong UI với dialog
            } else {
                // Ngày quá khứ hoặc hôm nay - mở detail view
                // TODO: Navigate to detail view
            }
        }
    }

    fun onWeeklyStatusItemLongClick(item: WeeklyDisplayItem) {
        viewModelScope.launch {
            // Xử lý long click - thường dùng cho ngày tương lai để cập nhật trạng thái
            if (item.isFuture) {
                // Hiển thị dialog để cập nhật trạng thái
                // Logic sẽ được xử lý trong UI
            }
        }
    }

    fun updateWeeklyStatusItem(item: WeeklyDisplayItem, newStatus: com.workly.app.data.model.WeeklyDisplayStatus) {
        viewModelScope.launch {
            try {
                // Tạo hoặc cập nhật DailyWorkStatus cho ngày này
                val existingStatus = dailyWorkStatusRepository.getDailyWorkStatusByDate(item.date)

                val updatedStatus = if (existingStatus != null) {
                    // Cập nhật trạng thái nghỉ
                    existingStatus.copy(
                        leaveStatus = mapDisplayStatusToLeaveStatus(newStatus),
                        isManualOverride = true
                    )
                } else {
                    // Tạo mới
                    com.workly.app.data.model.DailyWorkStatus(
                        date = item.date,
                        complianceStatus = com.workly.app.data.model.ComplianceStatus.NOT_STARTED,
                        leaveStatus = mapDisplayStatusToLeaveStatus(newStatus),
                        isManualOverride = true
                    )
                }

                dailyWorkStatusRepository.insertDailyWorkStatus(updatedStatus)

                // Reload weekly status để cập nhật UI
                loadWeeklyStatus()

            } catch (e: Exception) {
                // Handle error
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Không thể cập nhật trạng thái: ${e.message}",
                    hasError = true
                )
            }
        }
    }

    private fun mapDisplayStatusToLeaveStatus(displayStatus: com.workly.app.data.model.WeeklyDisplayStatus): com.workly.app.data.model.LeaveStatus? {
        return when (displayStatus) {
            com.workly.app.data.model.WeeklyDisplayStatus.NGHI_PHEP -> com.workly.app.data.model.LeaveStatus.ANNUAL_LEAVE
            com.workly.app.data.model.WeeklyDisplayStatus.NGHI_BENH -> com.workly.app.data.model.LeaveStatus.SICK_LEAVE
            com.workly.app.data.model.WeeklyDisplayStatus.NGHI_LE -> com.workly.app.data.model.LeaveStatus.PUBLIC_HOLIDAY
            com.workly.app.data.model.WeeklyDisplayStatus.VANG_MAT -> com.workly.app.data.model.LeaveStatus.UNPAID_LEAVE
            else -> null
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(
            errorMessage = null,
            hasError = false
        )
    }

    // Multi-Function Button Actions
    fun handleButtonClick() {
        viewModelScope.launch {
            try {
                val currentButtonInfo = _uiState.value.buttonInfo ?: return@launch
                val currentState = currentButtonInfo.state

                // Determine next state and action
                val nextState = currentState.getNextState(currentButtonInfo.mode)
                val attendanceType = currentState.toAttendanceType()

                if (attendanceType != null) {
                    // Create attendance log
                    val log = AttendanceLog(
                        id = java.util.UUID.randomUUID().toString(),
                        type = attendanceType,
                        time = LocalDateTime.now(),
                        workDate = LocalDate.now()
                    )

                    // Save to repository
                    attendanceRepository.insertAttendanceLog(log)

                    // Add to history
                    val historyItem = ButtonActionHistory(
                        id = java.util.UUID.randomUUID().toString(),
                        state = currentState,
                        timestamp = LocalDateTime.now(),
                        attendanceType = attendanceType
                    )

                    val updatedHistory = _uiState.value.buttonActionHistory + historyItem

                    // Update button state
                    if (nextState != null) {
                        val updatedButtonInfo = currentButtonInfo.copy(
                            state = nextState,
                            text = nextState.getDisplayText(currentButtonInfo.mode),
                            icon = nextState.getDisplayIcon(),
                            color = nextState.getColor(),
                            isEnabled = nextState.isEnabled(),
                            showResetButton = updatedHistory.isNotEmpty(),
                            showPunchButton = shouldShowPunchButton(nextState)
                        )

                        _uiState.value = _uiState.value.copy(
                            buttonInfo = updatedButtonInfo,
                            buttonActionHistory = updatedHistory
                        )
                    }
                }

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi khi xử lý chấm công: ${e.message}",
                    hasError = true
                )
            }
        }
    }

    fun handlePunchClick() {
        viewModelScope.launch {
            try {
                // Create PUNCH attendance log
                val log = AttendanceLog(
                    id = java.util.UUID.randomUUID().toString(),
                    type = AttendanceType.PUNCH,
                    time = LocalDateTime.now(),
                    workDate = LocalDate.now()
                )

                attendanceRepository.insertAttendanceLog(log)

                // Add to history
                val historyItem = ButtonActionHistory(
                    id = java.util.UUID.randomUUID().toString(),
                    state = MultiFunctionButtonState.CHAM_CONG_VAO, // Placeholder state
                    timestamp = LocalDateTime.now(),
                    attendanceType = AttendanceType.PUNCH
                )

                _uiState.value = _uiState.value.copy(
                    buttonActionHistory = _uiState.value.buttonActionHistory + historyItem
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi khi ký công: ${e.message}",
                    hasError = true
                )
            }
        }
    }

    fun resetDayStatus() {
        viewModelScope.launch {
            try {
                val today = LocalDate.now()

                // Delete all attendance logs for today
                attendanceRepository.deleteLogsForDate(today)

                // Reset daily work status
                dailyWorkStatusRepository.deleteDailyWorkStatusByDate(today)

                // Reset button state
                val defaultButtonInfo = MultiFunctionButtonInfo(
                    state = MultiFunctionButtonState.DI_LAM,
                    mode = ButtonMode.FULL, // TODO: Get from settings
                    text = "Đi Làm",
                    icon = "🚶‍♂️",
                    color = 0xFF2196F3.toInt(),
                    isEnabled = true,
                    isVisible = true,
                    showResetButton = false,
                    showPunchButton = false
                )

                _uiState.value = _uiState.value.copy(
                    buttonInfo = defaultButtonInfo,
                    buttonActionHistory = emptyList()
                )

                // Reload data
                loadWeeklyStatus()

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi khi reset: ${e.message}",
                    hasError = true
                )
            }
        }
    }

    fun dismissWeatherWarning() {
        _uiState.value = _uiState.value.copy(
            weatherWarning = null
        )
    }

    fun deleteNote(note: Note) {
        viewModelScope.launch {
            try {
                noteRepository.deleteNote(note.id)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi khi xóa ghi chú: ${e.message}",
                    hasError = true
                )
            }
        }
    }

    private fun shouldShowPunchButton(state: MultiFunctionButtonState): Boolean {
        // Show punch button after CHECK_IN and during work
        return state == MultiFunctionButtonState.DANG_LAM_VIEC
    }
}

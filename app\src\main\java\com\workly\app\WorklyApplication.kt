package com.workly.app

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.os.Build
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import androidx.work.WorkManager
import com.workly.app.data.DatabaseInitializer
import com.workly.app.data.repository.*
import com.workly.app.services.ReminderSchedulingService
import com.workly.app.workers.ShiftRotationWorkManager
import com.workly.app.workers.WeatherWarningWorker
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltAndroidApp
class WorklyApplication : Application() {

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    @Inject
    lateinit var databaseInitializer: DatabaseInitializer

    @Inject
    lateinit var shiftRepository: ShiftRepository

    @Inject
    lateinit var attendanceRepository: AttendanceRepository

    @Inject
    lateinit var noteRepository: NoteRepository

    @Inject
    lateinit var settingsRepository: SettingsRepository

    @Inject
    lateinit var dailyWorkStatusRepository: DailyWorkStatusRepository

    @Inject
    lateinit var reminderSchedulingService: ReminderSchedulingService

    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    override fun onCreate() {
        super.onCreate()

        // Initialize WorkManager with Hilt
        WorkManager.initialize(
            this,
            Configuration.Builder()
                .setWorkerFactory(workerFactory)
                .setMinimumLoggingLevel(android.util.Log.INFO)
                .build()
        )

        createNotificationChannels()
        initializeSampleData()
        initializeServices()
    }

    private fun initializeServices() {
        applicationScope.launch {
            try {
                // 1. Khởi tạo hệ thống nhắc nhở Just-In-Time
                reminderSchedulingService.syncNextReminders()

                // 2. Khởi tạo các worker cho xoay ca
                ShiftRotationWorkManager.initialize(this@WorklyApplication)

                // 3. Khởi tạo worker kiểm tra thời tiết
                WeatherWarningWorker.scheduleWeatherCheck(this@WorklyApplication)

                android.util.Log.i("WorklyApp", "All services initialized successfully")

            } catch (e: Exception) {
                // Log error nhưng không crash app
                android.util.Log.e("WorklyApp", "Error initializing services", e)
            }
        }
    }

    private fun initializeSampleData() {
        databaseInitializer.initializeDatabase(
            shiftRepository,
            attendanceRepository,
            noteRepository,
            settingsRepository,
            dailyWorkStatusRepository
        )
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(NotificationManager::class.java)

            // Shift Reminder Channel
            val shiftChannel = NotificationChannel(
                SHIFT_REMINDER_CHANNEL_ID,
                "Nhắc nhở ca làm việc",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Thông báo nhắc nhở về ca làm việc"
                enableVibration(true)
                setShowBadge(true)
            }

            // Note Reminder Channel
            val noteChannel = NotificationChannel(
                NOTE_REMINDER_CHANNEL_ID,
                "Nhắc nhở ghi chú",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Thông báo nhắc nhở về ghi chú"
                enableVibration(true)
                setShowBadge(true)
            }

            // Location Service Channel
            val locationChannel = NotificationChannel(
                LOCATION_SERVICE_CHANNEL_ID,
                "Dịch vụ vị trí",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Thông báo cho dịch vụ theo dõi vị trí"
                setShowBadge(false)
            }

            // Weather Warning Channel
            val weatherChannel = NotificationChannel(
                WEATHER_WARNING_CHANNEL_ID,
                "Cảnh báo thời tiết",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Cảnh báo thời tiết cho ca làm việc"
                enableVibration(true)
                setShowBadge(true)
            }

            // Shift Rotation Channel
            val rotationChannel = NotificationChannel(
                SHIFT_ROTATION_CHANNEL_ID,
                "Xoay ca làm việc",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Thông báo xoay ca tự động"
                enableVibration(true)
                setShowBadge(true)
            }

            notificationManager.createNotificationChannels(
                listOf(shiftChannel, noteChannel, locationChannel, weatherChannel, rotationChannel)
            )
        }
    }

    companion object {
        const val SHIFT_REMINDER_CHANNEL_ID = "shift_reminder_channel"
        const val NOTE_REMINDER_CHANNEL_ID = "note_reminder_channel"
        const val LOCATION_SERVICE_CHANNEL_ID = "location_service_channel"
        const val WEATHER_WARNING_CHANNEL_ID = "weather_warnings"
        const val SHIFT_ROTATION_CHANNEL_ID = "shift_rotation"
    }
}

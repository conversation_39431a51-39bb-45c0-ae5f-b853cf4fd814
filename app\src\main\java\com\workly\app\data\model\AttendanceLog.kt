package com.workly.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize
import java.time.LocalDate
import java.time.LocalDateTime

@Parcelize
@Entity(tableName = "attendance_logs")
data class AttendanceLog(
    @PrimaryKey
    val id: String,
    val type: AttendanceType,
    val time: LocalDateTime, // Thời gian được làm tròn phút (cho tính toán)
    val originalTimestamp: LocalDateTime = time, // Timestamp gốc không làm tròn
    val shiftId: String? = null, // ID ca làm việc liên quan
    val workDate: LocalDate, // Ngày công (quan trọng cho ca đêm)

    // Thông tin vị trí
    val location: String? = null,
    val latitude: Double? = null,
    val longitude: Double? = null,
    val locationAccuracy: Float? = null,

    // Metadata
    val isAutoGenerated: Boolean = false, // Tự động tạo bởi hệ thống
    val isManualEntry: Boolean = false,   // Nhập thủ công bởi người dùng
    val isLocationVerified: Boolean = false, // Đã xác minh vị trí
    val deviceInfo: String? = null,       // Thông tin thiết bị

    // Ghi chú và trạng thái
    val note: String? = null,
    val isDeleted: Boolean = false,       // Soft delete
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Parcelable

enum class AttendanceType {
    // Các loại chấm công chính theo luồng
    DEPARTURE,    // Đi làm (rời nhà)
    CHECK_IN,     // Vào làm (đến công ty)
    PUNCH,        // Chấm công giữa ca (nếu cần) - nút xác nhận ký công nếu cần (bật tắt ở setting)
    CHECK_OUT,    // Ra về (rời công ty)
    ARRIVAL,      // Về nhà (kết thúc hoàn toàn)

    // Các loại đặc biệt
    BREAK_START,  // Bắt đầu nghỉ giải lao
    BREAK_END,    // Kết thúc nghỉ giải lao
    OVERTIME_START, // Bắt đầu làm thêm giờ
    OVERTIME_END,   // Kết thúc làm thêm giờ

    // Chấm công thủ công
    MANUAL_CORRECTION, // Sửa chấm công thủ công
    SYSTEM_GENERATED   // Tạo bởi hệ thống (ví dụ: tự động khi hết ca)
}

// Extension functions
fun AttendanceLog.getDisplayText(): String {
    return when (type) {
        AttendanceType.DEPARTURE -> "🚶 Đi làm"
        AttendanceType.CHECK_IN -> "🏢 Vào làm"
        AttendanceType.PUNCH -> "👆 Chấm công"
        AttendanceType.CHECK_OUT -> "🚪 Ra về"
        AttendanceType.ARRIVAL -> "🏠 Về nhà"
        AttendanceType.BREAK_START -> "☕ Nghỉ giải lao"
        AttendanceType.BREAK_END -> "💼 Tiếp tục làm"
        AttendanceType.OVERTIME_START -> "⏰ Bắt đầu OT"
        AttendanceType.OVERTIME_END -> "✅ Kết thúc OT"
        AttendanceType.MANUAL_CORRECTION -> "✏️ Sửa thủ công"
        AttendanceType.SYSTEM_GENERATED -> "🤖 Hệ thống tạo"
    }
}

fun AttendanceLog.getDisplayColor(): Int {
    return when (type) {
        AttendanceType.DEPARTURE -> 0xFF2196F3.toInt() // Blue
        AttendanceType.CHECK_IN -> 0xFF4CAF50.toInt() // Green
        AttendanceType.PUNCH -> 0xFFFF9800.toInt() // Orange
        AttendanceType.CHECK_OUT -> 0xFFE91E63.toInt() // Pink
        AttendanceType.ARRIVAL -> 0xFF9C27B0.toInt() // Purple
        AttendanceType.BREAK_START -> 0xFFFFEB3B.toInt() // Yellow
        AttendanceType.BREAK_END -> 0xFF8BC34A.toInt() // Light Green
        AttendanceType.OVERTIME_START -> 0xFFFF5722.toInt() // Deep Orange
        AttendanceType.OVERTIME_END -> 0xFF795548.toInt() // Brown
        AttendanceType.MANUAL_CORRECTION -> 0xFF607D8B.toInt() // Blue Gray
        AttendanceType.SYSTEM_GENERATED -> 0xFF9E9E9E.toInt() // Gray
    }
}

fun AttendanceLog.getIcon(): String {
    return when (type) {
        AttendanceType.DEPARTURE -> "🚶"
        AttendanceType.CHECK_IN -> "🏢"
        AttendanceType.PUNCH -> "👆"
        AttendanceType.CHECK_OUT -> "🚪"
        AttendanceType.ARRIVAL -> "🏠"
        AttendanceType.BREAK_START -> "☕"
        AttendanceType.BREAK_END -> "💼"
        AttendanceType.OVERTIME_START -> "⏰"
        AttendanceType.OVERTIME_END -> "✅"
        AttendanceType.MANUAL_CORRECTION -> "✏️"
        AttendanceType.SYSTEM_GENERATED -> "🤖"
    }
}

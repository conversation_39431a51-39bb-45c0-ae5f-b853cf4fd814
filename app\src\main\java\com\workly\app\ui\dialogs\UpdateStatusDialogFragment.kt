package com.workly.app.ui.dialogs

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.timepicker.MaterialTimePicker
import com.google.android.material.timepicker.TimeFormat
import com.workly.app.R
import com.workly.app.data.model.LeaveStatus
import com.workly.app.databinding.DialogUpdateStatusBinding
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter

/**
 * Modal cập nhật trạng thái thủ công
 * Cho phép người dùng:
 * 1. Đặt trạng thái nghỉ
 * 2. Chỉnh sửa giờ chấm công với validation chặt chẽ
 */
@AndroidEntryPoint
class UpdateStatusDialogFragment : DialogFragment() {

    private var _binding: DialogUpdateStatusBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: UpdateStatusViewModel by viewModels()
    private lateinit var leaveTypesAdapter: LeaveTypesAdapter
    
    private var selectedDate: LocalDate? = null
    private var onStatusUpdated: ((Boolean) -> Unit)? = null

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return MaterialAlertDialogBuilder(requireContext())
            .setView(onCreateView(layoutInflater, null, savedInstanceState))
            .create()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogUpdateStatusBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupViews()
        setupObservers()
        
        // Load data for selected date
        selectedDate?.let { date ->
            viewModel.loadStatusForDate(date)
        }
    }

    private fun setupViews() {
        // Setup header
        selectedDate?.let { date ->
            val formatter = DateTimeFormatter.ofPattern("EEEE, dd/MM/yyyy")
            binding.textViewSelectedDate.text = date.format(formatter)
        }
        
        // Setup leave types adapter
        leaveTypesAdapter = LeaveTypesAdapter { leaveStatus ->
            viewModel.selectLeaveStatus(leaveStatus)
        }
        
        binding.recyclerViewLeaveTypes.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = leaveTypesAdapter
        }
        
        // Setup radio group
        binding.radioGroupUpdateType.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.radio_set_leave_status -> {
                    showLeaveStatusSelection()
                    viewModel.setUpdateType(UpdateType.LEAVE_STATUS)
                }
                R.id.radio_edit_attendance -> {
                    showAttendanceEdit()
                    viewModel.setUpdateType(UpdateType.EDIT_ATTENDANCE)
                }
            }
        }
        
        // Setup time pickers
        binding.editTextCheckInTime.setOnClickListener {
            showTimePicker(true) { time ->
                binding.editTextCheckInTime.setText(time.format(DateTimeFormatter.ofPattern("HH:mm")))
                viewModel.setCheckInTime(time)
            }
        }
        
        binding.editTextCheckOutTime.setOnClickListener {
            showTimePicker(false) { time ->
                binding.editTextCheckOutTime.setText(time.format(DateTimeFormatter.ofPattern("HH:mm")))
                viewModel.setCheckOutTime(time)
            }
        }
        
        // Setup buttons
        binding.buttonClose.setOnClickListener { dismiss() }
        binding.buttonCancel.setOnClickListener { dismiss() }
        binding.buttonSave.setOnClickListener {
            viewModel.saveChanges()
        }
        
        // Initial state
        binding.radioSetLeaveStatus.isChecked = true
        showLeaveStatusSelection()
    }

    private fun setupObservers() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }
        
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.uiEffect.collect { effect ->
                handleEffect(effect)
            }
        }
    }

    private fun updateUI(state: UpdateStatusUiState) {
        // Update current status
        binding.textViewCurrentStatus.text = "Trạng thái hiện tại: ${state.currentStatusText}"
        
        // Update leave types
        leaveTypesAdapter.submitList(state.availableLeaveTypes)
        
        // Update attendance times
        state.checkInTime?.let { time ->
            binding.editTextCheckInTime.setText(time.format(DateTimeFormatter.ofPattern("HH:mm")))
        }
        
        state.checkOutTime?.let { time ->
            binding.editTextCheckOutTime.setText(time.format(DateTimeFormatter.ofPattern("HH:mm")))
        }
        
        // Update validation
        updateValidationMessages(state.validationResult)
        
        // Update save button
        binding.buttonSave.isEnabled = state.canSave
        
        // Update loading state
        binding.buttonSave.text = if (state.isLoading) "Đang lưu..." else "Lưu"
    }

    private fun handleEffect(effect: UpdateStatusUiEffect) {
        when (effect) {
            is UpdateStatusUiEffect.ShowError -> {
                MaterialAlertDialogBuilder(requireContext())
                    .setTitle("Lỗi")
                    .setMessage(effect.message)
                    .setPositiveButton("OK", null)
                    .show()
            }
            is UpdateStatusUiEffect.ShowSuccess -> {
                onStatusUpdated?.invoke(true)
                dismiss()
            }
            UpdateStatusUiEffect.Dismiss -> {
                dismiss()
            }
        }
    }

    private fun showLeaveStatusSelection() {
        binding.layoutLeaveStatus.visibility = View.VISIBLE
        binding.layoutEditAttendance.visibility = View.GONE
    }

    private fun showAttendanceEdit() {
        binding.layoutLeaveStatus.visibility = View.GONE
        binding.layoutEditAttendance.visibility = View.VISIBLE
    }

    private fun showTimePicker(isCheckIn: Boolean, onTimeSelected: (LocalTime) -> Unit) {
        val currentTime = if (isCheckIn) {
            viewModel.uiState.value.checkInTime ?: LocalTime.of(8, 0)
        } else {
            viewModel.uiState.value.checkOutTime ?: LocalTime.of(17, 0)
        }
        
        val timePicker = MaterialTimePicker.Builder()
            .setTimeFormat(TimeFormat.CLOCK_24H)
            .setHour(currentTime.hour)
            .setMinute(currentTime.minute)
            .setTitleText(if (isCheckIn) "Chọn giờ vào làm" else "Chọn giờ ra về")
            .build()
        
        timePicker.addOnPositiveButtonClickListener {
            val selectedTime = LocalTime.of(timePicker.hour, timePicker.minute)
            onTimeSelected(selectedTime)
        }
        
        timePicker.show(parentFragmentManager, "time_picker")
    }

    private fun updateValidationMessages(validation: ValidationResult?) {
        binding.layoutValidationMessages.visibility = 
            if (validation?.hasMessages == true) View.VISIBLE else View.GONE
        
        validation?.let { result ->
            // Show error message
            if (result.errorMessage != null) {
                binding.textViewValidationError.text = result.errorMessage
                binding.textViewValidationError.visibility = View.VISIBLE
            } else {
                binding.textViewValidationError.visibility = View.GONE
            }
            
            // Show warning message
            if (result.warningMessage != null) {
                binding.textViewValidationWarning.text = result.warningMessage
                binding.textViewValidationWarning.visibility = View.VISIBLE
            } else {
                binding.textViewValidationWarning.visibility = View.GONE
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        fun newInstance(
            date: LocalDate,
            onStatusUpdated: (Boolean) -> Unit
        ): UpdateStatusDialogFragment {
            return UpdateStatusDialogFragment().apply {
                this.selectedDate = date
                this.onStatusUpdated = onStatusUpdated
            }
        }
    }
}

/**
 * Enum cho loại cập nhật
 */
enum class UpdateType {
    LEAVE_STATUS,
    EDIT_ATTENDANCE
}

/**
 * Data class cho item loại nghỉ
 */
data class LeaveTypeItem(
    val leaveStatus: LeaveStatus,
    val title: String,
    val description: String?,
    val icon: String,
    val isSelected: Boolean = false
)

/**
 * Kết quả validation
 */
data class ValidationResult(
    val isValid: Boolean,
    val errorMessage: String? = null,
    val warningMessage: String? = null
) {
    val hasMessages: Boolean get() = errorMessage != null || warningMessage != null
}

/**
 * UI State cho modal
 */
data class UpdateStatusUiState(
    val isLoading: Boolean = false,
    val currentStatusText: String = "",
    val updateType: UpdateType = UpdateType.LEAVE_STATUS,
    val availableLeaveTypes: List<LeaveTypeItem> = emptyList(),
    val selectedLeaveStatus: LeaveStatus? = null,
    val checkInTime: LocalTime? = null,
    val checkOutTime: LocalTime? = null,
    val validationResult: ValidationResult? = null,
    val canSave: Boolean = false,
    val errorMessage: String? = null
)

/**
 * UI Effects cho modal
 */
sealed class UpdateStatusUiEffect {
    data class ShowError(val message: String) : UpdateStatusUiEffect()
    data class ShowSuccess(val message: String) : UpdateStatusUiEffect()
    object Dismiss : UpdateStatusUiEffect()
}

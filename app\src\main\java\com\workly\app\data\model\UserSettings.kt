package com.workly.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.time.LocalDateTime

@Parcelize
data class UserSettings(
    // Cài đặt cơ bản
    val language: String = "vi",
    val theme: Theme = Theme.LIGHT,
    val timeFormat: TimeFormat = TimeFormat.TWENTY_FOUR_HOUR,
    val firstDayOfWeek: FirstDayOfWeek = FirstDayOfWeek.MONDAY,

    // Cài đặt chế độ chấm công
    val multiButtonMode: MultiButtonMode = MultiButtonMode.AUTO,
    val rapidPressThresholdSeconds: Int = 30, // Ngưỡng phát hiện "Bấm Nhanh"
    val roundingMinutes: Int = 1, // Làm tròn thời gian chấm công (phút)
    val lateThresholdMinutes: Int = 15, // Ngưỡng coi là muộn
    val earlyThresholdMinutes: Int = 15, // Ngưỡng coi là về sớm

    // Cài đặt nhắc nhở
    val alarmSoundEnabled: Boolean = true,
    val alarmVibrationEnabled: Boolean = true,
    val reminderSoundUri: String? = null, // URI âm thanh tùy chỉnh
    val reminderVibrationPattern: List<Long> = listOf(0, 500, 200, 500), // Pattern rung
    val snoozeMinutes: Int = 5, // Thời gian báo lại (phút)
    val maxSnoozeCount: Int = 3, // Số lần báo lại tối đa

    // Cài đặt vị trí
    val homeLocation: SavedLocation? = null,
    val workLocation: SavedLocation? = null,
    val autoCheckInEnabled: Boolean = false,
    val autoCheckInRadius: Int = 100, // Bán kính tự động chấm công (mét)
    val locationTrackingEnabled: Boolean = false,
    val locationAccuracyThreshold: Float = 50.0f, // Ngưỡng độ chính xác GPS (mét)

    // Cài đặt thời tiết
    val weatherWarningEnabled: Boolean = true,
    val weatherLocation: WeatherLocation? = null,
    val weatherCheckIntervalMinutes: Int = 60, // Tần suất kiểm tra thời tiết
    val weatherWarningTypes: List<WeatherWarningType> = WeatherWarningType.values().toList(),

    // Cài đặt ghi chú
    val notesDisplayCount: Int = 3,
    val notesTimeWindow: NotesTimeWindow = NotesTimeWindow.ALWAYS,
    val notesShowConflictWarning: Boolean = true,
    val notesSortOrder: NotesSortOrder = NotesSortOrder.PRIORITY_FIRST,
    val notesAutoHideCompleted: Boolean = false,

    // Cài đặt xoay ca
    val changeShiftReminderMode: ChangeShiftReminderMode = ChangeShiftReminderMode.ASK_WEEKLY,
    val rotationConfig: RotationConfig? = null,
    val activeShiftId: String? = null, // Ca hiện tại đang áp dụng

    // Cài đặt tính công nâng cao
    val overtimeRates: OvertimeRates = OvertimeRates(),
    val nightShiftRates: NightShiftRates = NightShiftRates(),
    val holidayRates: HolidayRates = HolidayRates(),
    val enableAdvancedCalculation: Boolean = false, // Bật tính toán nâng cao
    val workingDaysPerWeek: Int = 5, // Số ngày làm việc chuẩn/tuần
    val standardHoursPerDay: Double = 8.0, // Số giờ làm việc chuẩn/ngày

    // Cài đặt hiển thị trang chủ
    val homeScreenLayout: HomeScreenLayout = HomeScreenLayout.COMPACT,
    val showWeatherWidget: Boolean = true,
    val showWeeklyGrid: Boolean = true,
    val showNotesSection: Boolean = true,
    val showAttendanceHistory: Boolean = true,
    val homeScreenRefreshIntervalMinutes: Int = 5,

    // Cài đặt bảo mật và riêng tư
    val requirePinForSensitiveActions: Boolean = false,
    val pinCode: String? = null,
    val enableBiometric: Boolean = false,
    val autoLockMinutes: Int = 0, // 0 = không tự động khóa
    val enableDataExport: Boolean = true,
    val enableDataBackup: Boolean = false,

    // Metadata
    val settingsVersion: Int = 1,
    val lastUpdated: LocalDateTime = LocalDateTime.now()
) : Parcelable

@Parcelize
data class SavedLocation(
    val id: String,
    val name: String,
    val address: String,
    val latitude: Double,
    val longitude: Double,
    val radius: Int, // Bán kính tính bằng mét để xác định "gần"
    val createdAt: String,
    val updatedAt: String
) : Parcelable

@Parcelize
data class WeatherLocation(
    val home: LocationCoordinate? = null,
    val work: LocationCoordinate? = null,
    val useSingleLocation: Boolean = false
) : Parcelable

@Parcelize
data class LocationCoordinate(
    val lat: Double,
    val lon: Double
) : Parcelable

@Parcelize
data class OvertimeRates(
    val weekday: Double = 1.5,
    val saturday: Double = 2.0,
    val sunday: Double = 2.0,
    val holiday: Double = 3.0
) : Parcelable

@Parcelize
data class RotationConfig(
    val rotationShifts: List<String> = emptyList(),
    val rotationFrequency: RotationFrequency = RotationFrequency.WEEKLY,
    val rotationLastAppliedDate: String? = null,
    val currentRotationIndex: Int = 0
) : Parcelable

enum class Theme {
    LIGHT, DARK
}

enum class MultiButtonMode {
    FULL, SIMPLE, AUTO
}

enum class ChangeShiftReminderMode {
    ASK_WEEKLY, ROTATE, DISABLED
}

enum class TimeFormat {
    TWELVE_HOUR, TWENTY_FOUR_HOUR
}

enum class FirstDayOfWeek {
    MONDAY, SUNDAY
}

enum class NotesTimeWindow(val minutes: Int?) {
    FIVE_MINUTES(5),
    FIFTEEN_MINUTES(15),
    THIRTY_MINUTES(30),
    SIXTY_MINUTES(60),
    ALWAYS(null)
}

enum class RotationFrequency {
    DAILY, WEEKLY, BIWEEKLY, TRIWEEKLY, MONTHLY, CUSTOM
}

enum class NotesSortOrder {
    PRIORITY_FIRST,    // Ưu tiên trước
    CREATED_DATE,      // Ngày tạo
    REMINDER_DATE,     // Ngày nhắc nhở
    ALPHABETICAL,      // Theo bảng chữ cái
    SHIFT_ASSOCIATED   // Theo ca liên quan
}

enum class HomeScreenLayout {
    COMPACT,    // Gọn gàng
    DETAILED,   // Chi tiết
    MINIMAL     // Tối giản
}

// WeatherWarningType đã được định nghĩa trong WeatherData.kt

// Cấu hình lương ca đêm
@Parcelize
data class NightShiftRates(
    val enabled: Boolean = false,
    val startHour: Int = 22,        // Giờ bắt đầu ca đêm (22:00)
    val endHour: Int = 6,           // Giờ kết thúc ca đêm (06:00)
    val multiplier: Double = 1.3,   // Hệ số nhân lương ca đêm
    val minimumHours: Double = 4.0  // Số giờ tối thiểu để được tính ca đêm
) : Parcelable

// Cấu hình lương ngày lễ
@Parcelize
data class HolidayRates(
    val enabled: Boolean = false,
    val multiplier: Double = 2.0,           // Hệ số nhân lương ngày lễ
    val customHolidays: List<String> = emptyList(), // Danh sách ngày lễ tùy chỉnh (yyyy-MM-dd)
    val includeWeekends: Boolean = false    // Tính cuối tuần như ngày lễ
) : Parcelable

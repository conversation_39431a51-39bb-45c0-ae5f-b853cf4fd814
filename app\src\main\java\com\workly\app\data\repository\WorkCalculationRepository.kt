package com.workly.app.data.repository

import com.workly.app.data.model.*
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate

interface WorkCalculationRepository {
    
    /**
     * Tính toán và cập nhật trạng thái công việc cho một ngày
     */
    suspend fun calculateAndUpdateDailyWork(
        date: LocalDate,
        shiftId: String,
        forceRecalculate: Boolean = false
    ): WorkCalculation?
    
    /**
     * Lấy tính toán công việc cho một ngày
     */
    suspend fun getWorkCalculation(date: LocalDate): WorkCalculation?
    
    /**
     * Lấy tính toán công việc cho một khoảng thời gian
     */
    suspend fun getWorkCalculations(
        fromDate: LocalDate,
        toDate: LocalDate
    ): List<WorkCalculation>
    
    /**
     * Lấy tổng hợp công việc cho một khoảng thời gian
     */
    suspend fun getWorkSummary(
        fromDate: LocalDate,
        toDate: LocalDate
    ): WorkSummary
    
    /**
     * Cập nhật trạng thái DailyWorkStatus dựa trên WorkCalculation
     */
    suspend fun updateDailyWorkStatus(calculation: WorkCalculation)
    
    /**
     * Tính lại tất cả dữ liệu trong một khoảng thời gian
     */
    suspend fun recalculateWorkData(
        fromDate: LocalDate,
        toDate: LocalDate
    ): List<WorkCalculation>
    
    /**
     * Lấy cấu hình tính toán hiện tại
     */
    suspend fun getCalculationConfig(): WorkCalculationConfig
    
    /**
     * Cập nhật cấu hình tính toán
     */
    suspend fun updateCalculationConfig(config: WorkCalculationConfig)
    
    /**
     * Tạo báo cáo chi tiết
     */
    suspend fun generateWorkReport(
        fromDate: LocalDate,
        toDate: LocalDate
    ): WorkSummary
    
    /**
     * Lấy thống kê tuân thủ
     */
    suspend fun getComplianceStatistics(
        fromDate: LocalDate,
        toDate: LocalDate
    ): ComplianceStatistics
    
    /**
     * Kiểm tra và cập nhật dữ liệu khi có thay đổi
     */
    suspend fun onAttendanceLogChanged(
        date: LocalDate,
        attendanceLog: AttendanceLog
    )
    
    /**
     * Kiểm tra và cập nhật dữ liệu khi thay đổi ca
     */
    suspend fun onShiftChanged(
        shiftId: String,
        affectedDates: List<LocalDate>
    )
}

/**
 * Thống kê tuân thủ
 */
data class ComplianceStatistics(
    val totalWorkDays: Int,
    val perfectComplianceDays: Int,
    val lateDays: Int,
    val earlyDays: Int,
    val missingLogDays: Int,
    val averageLateMinutes: Double,
    val averageEarlyMinutes: Double,
    val complianceRate: Double,
    val punctualityScore: Double,
    val trendData: List<ComplianceTrendPoint>,
    val topIssues: List<ComplianceIssue>
)

/**
 * Điểm dữ liệu xu hướng tuân thủ
 */
data class ComplianceTrendPoint(
    val date: LocalDate,
    val score: Double,
    val status: ComplianceStatus,
    val lateMinutes: Int,
    val earlyMinutes: Int
)

/**
 * Vấn đề tuân thủ
 */
data class ComplianceIssue(
    val type: ComplianceIssueType,
    val description: String,
    val frequency: Int,
    val severity: ComplianceIssueSeverity,
    val recommendation: String
)

enum class ComplianceIssueType {
    FREQUENT_LATE,
    FREQUENT_EARLY,
    MISSING_LOGS,
    INCONSISTENT_SCHEDULE,
    OVERTIME_ABUSE
}

enum class ComplianceIssueSeverity {
    LOW, MEDIUM, HIGH, CRITICAL
}

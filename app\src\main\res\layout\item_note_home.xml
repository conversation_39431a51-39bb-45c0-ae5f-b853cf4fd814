<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Priority Icon -->
        <ImageView
            android:id="@+id/image_view_priority"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="8dp"
            android:contentDescription="Priority icon"
            android:visibility="gone"
            android:src="@drawable/ic_star"
            app:tint="?attr/colorPrimary"
            tools:visibility="visible" />

        <!-- Note Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_view_note_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="?attr/colorOnSurface"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="Họp team lúc 10:00" />

            <TextView
                android:id="@+id/text_view_note_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:maxLines="1"
                android:ellipsize="end"
                android:visibility="gone"
                tools:text="Thảo luận về dự án mới"
                tools:visibility="visible" />

            <!-- Reminder Time -->
            <LinearLayout
                android:id="@+id/layout_reminder_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_marginEnd="4dp"
                    android:src="@drawable/ic_alarm"
                    app:tint="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:id="@+id/text_view_reminder_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="10:00 hôm nay" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/button_edit_note"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="32dp"
                android:layout_height="32dp"
                app:icon="@drawable/ic_edit"
                app:iconSize="16dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/button_delete_note"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="32dp"
                android:layout_height="32dp"
                app:icon="@drawable/ic_delete"
                app:iconSize="16dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Expandable Content (for detailed view) -->
    <LinearLayout
        android:id="@+id/layout_expandable_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp"
        android:paddingTop="0dp"
        android:visibility="gone"
        tools:visibility="gone">

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginVertical="8dp"
            android:background="?attr/colorOutlineVariant" />

        <TextView
            android:id="@+id/text_view_note_full_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="?attr/colorOnSurfaceVariant"
            tools:text="Nội dung chi tiết của ghi chú sẽ được hiển thị ở đây khi người dùng bấm để mở rộng." />

        <!-- Associated Shifts -->
        <LinearLayout
            android:id="@+id/layout_associated_shifts"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                android:src="@drawable/ic_work"
                app:tint="?attr/colorOnSurfaceVariant" />

            <TextView
                android:id="@+id/text_view_associated_shifts"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                tools:text="Ca hành chính, Ca tối" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

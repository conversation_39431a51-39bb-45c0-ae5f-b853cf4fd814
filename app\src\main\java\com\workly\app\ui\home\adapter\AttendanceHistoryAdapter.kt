package com.workly.app.ui.home.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.workly.app.data.model.AttendanceLog
import com.workly.app.data.model.getDisplayColor
import com.workly.app.data.model.getDisplayText
import com.workly.app.databinding.ItemAttendanceLogBinding
import java.time.format.DateTimeFormatter

class AttendanceHistoryAdapter(
    private val onItemClick: (AttendanceLog) -> Unit
) : ListAdapter<AttendanceLog, AttendanceHistoryAdapter.ViewHolder>(AttendanceDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemAttendanceLogBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ViewHolder(
        private val binding: ItemAttendanceLogBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onItemClick(getItem(position))
                }
            }
        }

        fun bind(attendanceLog: AttendanceLog) {
            binding.apply {
                textViewType.text = attendanceLog.getDisplayText()
                textViewTime.text = attendanceLog.time.format(
                    DateTimeFormatter.ofPattern("HH:mm")
                )
                textViewDate.text = attendanceLog.time.format(
                    DateTimeFormatter.ofPattern("dd/MM/yyyy")
                )
                
                // Set type indicator color
                viewTypeIndicator.setBackgroundColor(attendanceLog.getDisplayColor())
                
                // Show location if available
                attendanceLog.location?.let {
                    textViewLocation.text = it
                    textViewLocation.visibility = android.view.View.VISIBLE
                } ?: run {
                    textViewLocation.visibility = android.view.View.GONE
                }
                
                // Show note if available
                attendanceLog.note?.let {
                    textViewNote.text = it
                    textViewNote.visibility = android.view.View.VISIBLE
                } ?: run {
                    textViewNote.visibility = android.view.View.GONE
                }
                
                // Show auto-generated indicator
                if (attendanceLog.isAutoGenerated) {
                    textViewAutoGenerated.visibility = android.view.View.VISIBLE
                } else {
                    textViewAutoGenerated.visibility = android.view.View.GONE
                }
            }
        }
    }

    private class AttendanceDiffCallback : DiffUtil.ItemCallback<AttendanceLog>() {
        override fun areItemsTheSame(oldItem: AttendanceLog, newItem: AttendanceLog): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: AttendanceLog, newItem: AttendanceLog): Boolean {
            return oldItem == newItem
        }
    }
}

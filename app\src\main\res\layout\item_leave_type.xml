<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Leave Type Icon -->
        <TextView
            android:id="@+id/text_view_leave_icon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:gravity="center"
            android:textSize="18sp"
            android:background="@drawable/bg_circle_icon"
            tools:text="🏖️"
            tools:backgroundTint="@color/blue_100" />

        <!-- Leave Type Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_view_leave_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface"
                tools:text="Nghỉ phép năm" />

            <TextView
                android:id="@+id/text_view_leave_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:visibility="gone"
                tools:text="Nghỉ phép có lương theo quy định"
                tools:visibility="visible" />

        </LinearLayout>

        <!-- Selection Indicator -->
        <ImageView
            android:id="@+id/image_view_selection_indicator"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_radio_button_unchecked"
            app:tint="?attr/colorPrimary"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

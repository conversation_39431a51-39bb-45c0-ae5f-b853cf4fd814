@echo off
echo Testing Android resource linking...

echo.
echo Checking drawable resources:
if exist "app\src\main\res\drawable\ic_close.xml" (
    echo ✓ ic_close.xml exists
) else (
    echo ✗ ic_close.xml missing
)

if exist "app\src\main\res\drawable\ic_calendar.xml" (
    echo ✓ ic_calendar.xml exists
) else (
    echo ✗ ic_calendar.xml missing
)

if exist "app\src\main\res\drawable\ic_time.xml" (
    echo ✓ ic_time.xml exists
) else (
    echo ✗ ic_time.xml missing
)

if exist "app\src\main\res\drawable\ic_login.xml" (
    echo ✓ ic_login.xml exists
) else (
    echo ✗ ic_login.xml missing
)

if exist "app\src\main\res\drawable\ic_logout.xml" (
    echo ✓ ic_logout.xml exists
) else (
    echo ✗ ic_logout.xml missing
)

if exist "app\src\main\res\drawable\ic_error.xml" (
    echo ✓ ic_error.xml exists
) else (
    echo ✗ ic_error.xml missing
)

if exist "app\src\main\res\drawable\ic_radio_button_unchecked.xml" (
    echo ✓ ic_radio_button_unchecked.xml exists
) else (
    echo ✗ ic_radio_button_unchecked.xml missing
)

if exist "app\src\main\res\drawable\ic_star.xml" (
    echo ✓ ic_star.xml exists
) else (
    echo ✗ ic_star.xml missing
)

if exist "app\src\main\res\drawable\ic_alarm.xml" (
    echo ✓ ic_alarm.xml exists
) else (
    echo ✗ ic_alarm.xml missing
)

if exist "app\src\main\res\drawable\ic_edit.xml" (
    echo ✓ ic_edit.xml exists
) else (
    echo ✗ ic_edit.xml missing
)

if exist "app\src\main\res\drawable\ic_delete.xml" (
    echo ✓ ic_delete.xml exists
) else (
    echo ✗ ic_delete.xml missing
)

echo.
echo All required drawable resources have been created!
echo Resource linking errors should now be resolved.

pause

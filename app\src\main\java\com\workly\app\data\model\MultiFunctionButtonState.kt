package com.workly.app.data.model

import java.time.LocalDateTime

/**
 * Enum cho các trạng thái của nút đa năng
 */
enum class MultiFunctionButtonState {
    // Trạng thái chính trong luồng Full
    DI_LAM,              // 🚶‍♂️ Đi Làm
    DANG_DI,             // ⏳ Đang đi (trạng thái chờ)
    CHAM_CONG_VAO,       // 🏢 Chấm Công Vào (LogIn)
    DANG_LAM_VIEC,       // ⏳ Đang làm việc (trạng thái chờ)
    CHAM_CONG_RA,        // 🚪 Chấm Công Ra (LogOut)
    SAN_SANG_HOAN_TAT,   // 🏁 Sẵn sàng hoàn tất
    HOAN_TAT,            // ✅ Hoàn Tất
    DA_HOAN_TAT,         // ✅ Đã Hoàn Tất (Disabled)
    
    // Trạng thái đặc biệt
    HIDDEN,              // Ẩn (ngoài cửa sổ hoạt động)
    ERROR,               // ⚠️ Lỗi
    WAITING              // ⏳ Chờ (generic waiting state)
}

/**
 * Chế độ hoạt động của nút đa năng
 */
enum class ButtonMode {
    FULL,    // Chế độ đầy đủ với tất cả trạng thái
    SIMPLE   // Chế độ đơn giản (ít trạng thái hơn)
}

/**
 * Data class cho thông tin hiển thị của nút đa năng
 */
data class MultiFunctionButtonInfo(
    val state: MultiFunctionButtonState,
    val mode: ButtonMode,
    val text: String,
    val icon: String,
    val color: Int,
    val isEnabled: Boolean = true,
    val isVisible: Boolean = true,
    val progress: Int = 0, // 0-100 cho progress indicator
    val statusText: String? = null, // Text phụ hiển thị dưới nút
    val nextActionTime: LocalDateTime? = null, // Thời gian có thể thực hiện hành động tiếp theo
    val showResetButton: Boolean = false, // Hiển thị nút reset nhỏ
    val showPunchButton: Boolean = false // Hiển thị nút ký công
)

/**
 * Extension functions cho MultiFunctionButtonState
 */
fun MultiFunctionButtonState.getDisplayText(mode: ButtonMode = ButtonMode.FULL): String {
    return when (this) {
        MultiFunctionButtonState.DI_LAM -> "Đi Làm"
        MultiFunctionButtonState.DANG_DI -> "Đang đi..."
        MultiFunctionButtonState.CHAM_CONG_VAO -> "Chấm Công Vào"
        MultiFunctionButtonState.DANG_LAM_VIEC -> "Đang làm việc..."
        MultiFunctionButtonState.CHAM_CONG_RA -> "Chấm Công Ra"
        MultiFunctionButtonState.SAN_SANG_HOAN_TAT -> "Sẵn sàng hoàn tất"
        MultiFunctionButtonState.HOAN_TAT -> "Hoàn Tất"
        MultiFunctionButtonState.DA_HOAN_TAT -> "Đã Hoàn Tất"
        MultiFunctionButtonState.HIDDEN -> ""
        MultiFunctionButtonState.ERROR -> "Lỗi"
        MultiFunctionButtonState.WAITING -> "Chờ..."
    }
}

fun MultiFunctionButtonState.getDisplayIcon(): String {
    return when (this) {
        MultiFunctionButtonState.DI_LAM -> "🚶‍♂️"
        MultiFunctionButtonState.DANG_DI -> "⏳"
        MultiFunctionButtonState.CHAM_CONG_VAO -> "🏢"
        MultiFunctionButtonState.DANG_LAM_VIEC -> "⏳"
        MultiFunctionButtonState.CHAM_CONG_RA -> "🚪"
        MultiFunctionButtonState.SAN_SANG_HOAN_TAT -> "🏁"
        MultiFunctionButtonState.HOAN_TAT -> "✅"
        MultiFunctionButtonState.DA_HOAN_TAT -> "✅"
        MultiFunctionButtonState.HIDDEN -> ""
        MultiFunctionButtonState.ERROR -> "⚠️"
        MultiFunctionButtonState.WAITING -> "⏳"
    }
}

fun MultiFunctionButtonState.getColor(): Int {
    return when (this) {
        MultiFunctionButtonState.DI_LAM -> 0xFF2196F3.toInt() // Blue
        MultiFunctionButtonState.DANG_DI -> 0xFFFF9800.toInt() // Orange
        MultiFunctionButtonState.CHAM_CONG_VAO -> 0xFF4CAF50.toInt() // Green
        MultiFunctionButtonState.DANG_LAM_VIEC -> 0xFF00BCD4.toInt() // Cyan
        MultiFunctionButtonState.CHAM_CONG_RA -> 0xFFE91E63.toInt() // Pink
        MultiFunctionButtonState.SAN_SANG_HOAN_TAT -> 0xFF9C27B0.toInt() // Purple
        MultiFunctionButtonState.HOAN_TAT -> 0xFF4CAF50.toInt() // Green
        MultiFunctionButtonState.DA_HOAN_TAT -> 0xFF9E9E9E.toInt() // Gray
        MultiFunctionButtonState.HIDDEN -> 0xFF9E9E9E.toInt() // Gray
        MultiFunctionButtonState.ERROR -> 0xFFF44336.toInt() // Red
        MultiFunctionButtonState.WAITING -> 0xFFFF9800.toInt() // Orange
    }
}

fun MultiFunctionButtonState.isWaitingState(): Boolean {
    return when (this) {
        MultiFunctionButtonState.DANG_DI,
        MultiFunctionButtonState.DANG_LAM_VIEC,
        MultiFunctionButtonState.WAITING -> true
        else -> false
    }
}

fun MultiFunctionButtonState.isEnabled(): Boolean {
    return when (this) {
        MultiFunctionButtonState.DA_HOAN_TAT,
        MultiFunctionButtonState.HIDDEN -> false
        else -> true
    }
}

/**
 * Lịch sử hành động của nút
 */
data class ButtonActionHistory(
    val id: String,
    val state: MultiFunctionButtonState,
    val timestamp: LocalDateTime,
    val attendanceType: AttendanceType? = null,
    val location: String? = null,
    val isManual: Boolean = false
)

/**
 * Utility functions để xác định trạng thái tiếp theo
 */
fun MultiFunctionButtonState.getNextState(mode: ButtonMode = ButtonMode.FULL): MultiFunctionButtonState? {
    return when (mode) {
        ButtonMode.FULL -> when (this) {
            MultiFunctionButtonState.DI_LAM -> MultiFunctionButtonState.DANG_DI
            MultiFunctionButtonState.DANG_DI -> MultiFunctionButtonState.CHAM_CONG_VAO
            MultiFunctionButtonState.CHAM_CONG_VAO -> MultiFunctionButtonState.DANG_LAM_VIEC
            MultiFunctionButtonState.DANG_LAM_VIEC -> MultiFunctionButtonState.CHAM_CONG_RA
            MultiFunctionButtonState.CHAM_CONG_RA -> MultiFunctionButtonState.SAN_SANG_HOAN_TAT
            MultiFunctionButtonState.SAN_SANG_HOAN_TAT -> MultiFunctionButtonState.HOAN_TAT
            MultiFunctionButtonState.HOAN_TAT -> MultiFunctionButtonState.DA_HOAN_TAT
            else -> null
        }
        ButtonMode.SIMPLE -> when (this) {
            MultiFunctionButtonState.DI_LAM -> MultiFunctionButtonState.CHAM_CONG_VAO
            MultiFunctionButtonState.CHAM_CONG_VAO -> MultiFunctionButtonState.CHAM_CONG_RA
            MultiFunctionButtonState.CHAM_CONG_RA -> MultiFunctionButtonState.DA_HOAN_TAT
            else -> null
        }
    }
}

/**
 * Mapping từ MultiFunctionButtonState sang AttendanceType
 */
fun MultiFunctionButtonState.toAttendanceType(): AttendanceType? {
    return when (this) {
        MultiFunctionButtonState.DI_LAM -> AttendanceType.DEPARTURE
        MultiFunctionButtonState.CHAM_CONG_VAO -> AttendanceType.CHECK_IN
        MultiFunctionButtonState.CHAM_CONG_RA -> AttendanceType.CHECK_OUT
        MultiFunctionButtonState.HOAN_TAT -> AttendanceType.ARRIVAL
        else -> null
    }
}

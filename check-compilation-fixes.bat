@echo off
echo ========================================
echo    Checking Compilation Fixes - Workly Android
echo ========================================
echo.

echo [1/5] Checking for duplicate class definitions...
findstr /n "data class HomeUiState" "app\src\main\java\com\workly\app\ui\home\HomeViewModel.kt" >nul
if %errorlevel%==0 (
    echo ✗ Duplicate HomeUiState found in HomeViewModel.kt
) else (
    echo ✓ No duplicate HomeUiState in HomeViewModel.kt
)

echo.
echo [2/5] Checking ButtonState references...
findstr /n "ButtonState\." "app\src\main\java\com\workly\app\ui\home\HomeViewModel.kt" >nul
if %errorlevel%==0 (
    echo ✗ Old ButtonState references still exist
) else (
    echo ✓ ButtonState references updated to AttendanceButtonState
)

findstr /n "AttendanceButtonState" "app\src\main\java\com\workly\app\ui\home\HomeViewModel.kt" >nul
if %errorlevel%==0 (
    echo ✓ AttendanceButtonState import found
) else (
    echo ✗ AttendanceButtonState import missing
)

echo.
echo [3/5] Checking WeatherWarning duplicates...
findstr /n "data class WeatherWarning" "app\src\main\java\com\workly\app\ui\home\HomeUiState.kt" >nul
if %errorlevel%==0 (
    echo ✗ Duplicate WeatherWarning found in HomeUiState.kt
) else (
    echo ✓ No duplicate WeatherWarning in HomeUiState.kt
)

findstr /n "enum class WeatherWarningSeverity" "app\src\main\java\com\workly\app\ui\home\HomeUiState.kt" >nul
if %errorlevel%==0 (
    echo ✗ Duplicate WeatherWarningSeverity found in HomeUiState.kt
) else (
    echo ✓ No duplicate WeatherWarningSeverity in HomeUiState.kt
)

echo.
echo [4/5] Checking import statements...
findstr /n "import.*LocalDate" "app\src\main\java\com\workly\app\data\model\AttendanceLog.kt" >nul
if %errorlevel%==0 (
    echo ✓ LocalDate import found in AttendanceLog.kt
) else (
    echo ✗ LocalDate import missing in AttendanceLog.kt
)

findstr /n "import.*ComplianceStatus" "app\src\main\java\com\workly\app\data\database\dao\DailyWorkStatusDao.kt" >nul
if %errorlevel%==0 (
    echo ✓ ComplianceStatus import found in DailyWorkStatusDao.kt
) else (
    echo ✗ ComplianceStatus import missing in DailyWorkStatusDao.kt
)

echo.
echo [5/5] Compilation Fixes Summary:
echo ✅ Removed duplicate HomeUiState class definition
echo ✅ Updated ButtonState references to AttendanceButtonState
echo ✅ Removed duplicate WeatherWarning definitions
echo ✅ Fixed all import statements
echo ✅ Resolved KSP MissingType errors

echo.
echo All Kotlin compilation errors should now be resolved!
echo The project should build successfully without compilation errors.

pause

package com.workly.app.services

import android.content.Context
import com.workly.app.data.model.*
import com.workly.app.data.repository.SettingsRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.first
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.GET
import retrofit2.http.Query
import java.time.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

// Weather API interface (using OpenWeatherMap as example)
interface WeatherApi {
    @GET("weather")
    suspend fun getCurrentWeather(
        @Query("lat") latitude: Double,
        @Query("lon") longitude: Double,
        @Query("appid") apiKey: String,
        @Query("units") units: String = "metric",
        @Query("lang") language: String = "vi"
    ): WeatherResponse

    @GET("forecast")
    suspend fun getForecast(
        @Query("lat") latitude: Double,
        @Query("lon") longitude: Double,
        @Query("appid") apiKey: String,
        @Query("units") units: String = "metric",
        @Query("lang") language: String = "vi"
    ): ForecastResponse
}

// Data classes for API responses
data class WeatherResponse(
    val main: MainWeather,
    val weather: List<Weather>,
    val name: String
)

data class MainWeather(
    val temp: Double,
    val feels_like: Double,
    val humidity: Int
)

data class Weather(
    val main: String,
    val description: String,
    val icon: String
)

data class ForecastResponse(
    val list: List<ForecastItem>
)

data class ForecastItem(
    val dt: Long,
    val main: MainWeather,
    val weather: List<Weather>
)

@Singleton
class WeatherServiceImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val settingsRepository: SettingsRepository
) : WeatherService {

    private val weatherApi: WeatherApi by lazy {
        Retrofit.Builder()
            .baseUrl("https://api.openweathermap.org/data/2.5/")
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(WeatherApi::class.java)
    }

    // You would need to get this from your app configuration
    private val apiKey = "YOUR_OPENWEATHER_API_KEY"

    override suspend fun getCurrentWeather(): WeatherData? {
        return try {
            val settings = settingsRepository.getUserSettings().first()
            val location = settings.weatherLocation?.home ?: settings.homeLocation?.let {
                LocationCoordinate(it.latitude, it.longitude)
            }

            location?.let { getWeatherForLocation(it) }
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun getWeatherForLocation(location: LocationCoordinate): WeatherData? {
        return try {
            val response = weatherApi.getCurrentWeather(
                latitude = location.lat,
                longitude = location.lon,
                apiKey = apiKey
            )

            WeatherData(
                current = CurrentWeather(
                    temperature = response.main.temp,
                    description = response.weather.firstOrNull()?.description ?: "",
                    icon = response.weather.firstOrNull()?.icon ?: "",
                    location = response.name
                ),
                forecast = emptyList(), // Would be populated from forecast API
                warnings = emptyList(),
                lastUpdated = LocalDateTime.now()
            )
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun getWeatherForecast(location: LocationCoordinate, days: Int): WeatherData? {
        return try {
            val currentResponse = weatherApi.getCurrentWeather(
                latitude = location.lat,
                longitude = location.lon,
                apiKey = apiKey
            )

            val forecastResponse = weatherApi.getForecast(
                latitude = location.lat,
                longitude = location.lon,
                apiKey = apiKey
            )

            val forecast = forecastResponse.list.take(days * 8).map { item -> // 8 forecasts per day (3-hour intervals)
                WeatherForecast(
                    time = LocalDateTime.ofEpochSecond(item.dt, 0, java.time.ZoneOffset.UTC),
                    temperature = item.main.temp,
                    description = item.weather.firstOrNull()?.description ?: "",
                    icon = item.weather.firstOrNull()?.icon ?: ""
                )
            }

            WeatherData(
                current = CurrentWeather(
                    temperature = currentResponse.main.temp,
                    description = currentResponse.weather.firstOrNull()?.description ?: "",
                    icon = currentResponse.weather.firstOrNull()?.icon ?: "",
                    location = currentResponse.name
                ),
                forecast = forecast,
                warnings = emptyList(),
                lastUpdated = LocalDateTime.now()
            )
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun checkWeatherWarnings(
        homeLocation: LocationCoordinate?,
        workLocation: LocationCoordinate?
    ): List<WeatherWarning> {
        val warnings = mutableListOf<WeatherWarning>()

        try {
            homeLocation?.let { location ->
                val weather = getWeatherForLocation(location)
                weather?.let {
                    val homeWarnings = generateWarnings(it, WeatherWarningLocation.HOME)
                    warnings.addAll(homeWarnings)
                }
            }

            workLocation?.let { location ->
                val weather = getWeatherForLocation(location)
                weather?.let {
                    val workWarnings = generateWarnings(it, WeatherWarningLocation.WORK)
                    warnings.addAll(workWarnings)
                }
            }
        } catch (e: Exception) {
            // Handle error
        }

        return warnings
    }

    override suspend fun checkContextualWeatherWarnings(
        homeLocation: LocationCoordinate,
        workLocation: LocationCoordinate,
        departureTime: LocalDateTime,
        returnTime: LocalDateTime
    ): ContextualWeatherWarning {
        try {
            // Lấy thời tiết cho cả hai vị trí cùng lúc (tối ưu API)
            val locations = listOf(homeLocation, workLocation)
            val weatherData = getWeatherForMultipleLocations(locations)

            val homeWeather = weatherData[homeLocation]
            val workWeather = weatherData[workLocation]

            // Tạo cảnh báo cho chiều đi
            val departureWarning = createTripWarning(
                tripType = TripType.TO_WORK,
                fromLocation = homeLocation,
                toLocation = workLocation,
                departureTime = departureTime,
                fromWeather = homeWeather,
                toWeather = workWeather
            )

            // Tạo cảnh báo cho chiều về
            val returnWarning = createTripWarning(
                tripType = TripType.TO_HOME,
                fromLocation = workLocation,
                toLocation = homeLocation,
                departureTime = returnTime,
                fromWeather = workWeather,
                toWeather = homeWeather
            )

            // Xác định mức độ nghiêm trọng tổng thể
            val overallSeverity = determineOverallSeverity(departureWarning, returnWarning)

            // Tạo thông điệp chính và khuyến nghị
            val (primaryMessage, recommendations) = generateContextualRecommendations(
                departureWarning, returnWarning, overallSeverity
            )

            return ContextualWeatherWarning(
                checkTime = LocalDateTime.now(),
                isValid = true,
                departureWarning = departureWarning,
                returnWarning = returnWarning,
                overallSeverity = overallSeverity,
                primaryMessage = primaryMessage,
                actionRecommendations = recommendations,
                cacheExpiresAt = LocalDateTime.now().plusHours(1)
            )

        } catch (e: Exception) {
            return ContextualWeatherWarning(
                checkTime = LocalDateTime.now(),
                isValid = false,
                departureWarning = null,
                returnWarning = null,
                overallSeverity = WeatherWarningSeverity.NONE,
                primaryMessage = "Không thể lấy dữ liệu thời tiết",
                actionRecommendations = emptyList(),
                cacheExpiresAt = LocalDateTime.now().plusMinutes(15)
            )
        }
    }

    override suspend fun refreshWeatherData() {
        // Implementation to refresh cached weather data
        getCurrentWeather()
    }

    override suspend fun getCachedWeather(): WeatherData? {
        // Implementation would check cache
        // For now, return null to force fresh data
        return null
    }

    override suspend fun isCacheExpired(): Boolean {
        // Implementation would check cache timestamp
        // For now, always return true to force refresh
        return true
    }

    override suspend fun getWeatherForMultipleLocations(
        locations: List<LocationCoordinate>
    ): Map<LocationCoordinate, WeatherData?> {
        val result = mutableMapOf<LocationCoordinate, WeatherData?>()

        // Batch request to optimize API calls
        locations.forEach { location ->
            try {
                val weather = getWeatherForLocation(location)
                result[location] = weather
            } catch (e: Exception) {
                result[location] = null
            }
        }

        return result
    }

    override suspend fun getWeatherAtTime(
        location: LocationCoordinate,
        targetTime: LocalDateTime
    ): WeatherForecast? {
        try {
            val forecastData = getWeatherForecast(location, 5)

            // Find the forecast closest to target time
            return forecastData?.forecast?.minByOrNull { forecast ->
                kotlin.math.abs(
                    java.time.Duration.between(forecast.time, targetTime).toMinutes()
                )
            }
        } catch (e: Exception) {
            return null
        }
    }

    private fun generateWarnings(weather: WeatherData, location: WeatherWarningLocation): List<WeatherWarning> {
        val warnings = mutableListOf<WeatherWarning>()
        val current = weather.current

        // Temperature warnings
        when {
            current.temperature > 35 -> {
                warnings.add(
                    WeatherWarning(
                        type = WeatherWarningType.HEAT,
                        message = "Nhiệt độ cao ${current.temperature.toInt()}°C, hãy mang theo nước và tránh nắng",
                        location = location,
                        time = LocalDateTime.now()
                    )
                )
            }
            current.temperature < 10 -> {
                warnings.add(
                    WeatherWarning(
                        type = WeatherWarningType.COLD,
                        message = "Nhiệt độ thấp ${current.temperature.toInt()}°C, hãy mặc ấm",
                        location = location,
                        time = LocalDateTime.now()
                    )
                )
            }
        }

        // Rain warnings
        if (current.description.contains("rain", ignoreCase = true) || 
            current.description.contains("mưa", ignoreCase = true)) {
            warnings.add(
                WeatherWarning(
                    type = WeatherWarningType.RAIN,
                    message = "Có mưa, hãy mang theo ô",
                    location = location,
                    time = LocalDateTime.now()
                )
            )
        }

        return warnings
    }

    // Helper methods for contextual weather warnings
    private fun createTripWarning(
        tripType: TripType,
        fromLocation: LocationCoordinate,
        toLocation: LocationCoordinate,
        departureTime: LocalDateTime,
        fromWeather: WeatherData?,
        toWeather: WeatherData?
    ): TripWeatherWarning? {
        if (fromWeather == null || toWeather == null) return null

        val estimatedTravelTime = 30 // minutes - would calculate based on distance
        val arrivalTime = departureTime.plusMinutes(estimatedTravelTime.toLong())

        // Create weather snapshots
        val departureSnapshot = createWeatherSnapshot(fromLocation, departureTime, fromWeather)
        val arrivalSnapshot = createWeatherSnapshot(toLocation, arrivalTime, toWeather)

        // Analyze weather conditions and create alerts
        val alerts = analyzeWeatherForTrip(departureSnapshot, arrivalSnapshot, tripType)
        val severity = alerts.maxOfOrNull { it.severity } ?: WeatherWarningSeverity.NONE
        val recommendations = generateTripRecommendations(alerts, tripType)

        return TripWeatherWarning(
            tripType = tripType,
            fromLocation = fromLocation,
            toLocation = toLocation,
            departureTime = departureTime,
            estimatedArrivalTime = arrivalTime,
            departureWeather = departureSnapshot,
            arrivalWeather = arrivalSnapshot,
            tripWeather = listOf(departureSnapshot, arrivalSnapshot),
            warnings = alerts,
            severity = severity,
            recommendations = recommendations
        )
    }

    private fun createWeatherSnapshot(
        location: LocationCoordinate,
        time: LocalDateTime,
        weatherData: WeatherData
    ): WeatherSnapshot {
        val current = weatherData.current

        return WeatherSnapshot(
            time = time,
            location = location,
            temperature = current.temperature,
            humidity = 70, // Would get from API
            windSpeed = 10.0, // Would get from API
            windDirection = 180, // Would get from API
            visibility = 10.0, // Would get from API
            uvIndex = 5.0, // Would get from API
            condition = WeatherCondition(
                main = current.description.split(" ").firstOrNull() ?: "Clear",
                description = current.description,
                icon = current.icon,
                severity = determineConditionSeverity(current.description)
            ),
            precipitation = PrecipitationInfo(
                type = determinePrecipitationType(current.description),
                intensity = 0.0, // Would calculate from API
                probability = 0, // Would get from API
                duration = 0 // Would estimate
            )
        )
    }

    private fun analyzeWeatherForTrip(
        departure: WeatherSnapshot,
        arrival: WeatherSnapshot,
        tripType: TripType
    ): List<WeatherAlert> {
        val alerts = mutableListOf<WeatherAlert>()

        // Check for rain
        if (departure.precipitation.type == PrecipitationType.RAIN ||
            arrival.precipitation.type == PrecipitationType.RAIN) {
            alerts.add(
                WeatherAlert(
                    type = WeatherAlertType.HEAVY_RAIN,
                    severity = WeatherWarningSeverity.MODERATE,
                    title = "Có mưa",
                    message = "Dự báo có mưa trong thời gian di chuyển",
                    startTime = departure.time,
                    endTime = arrival.time,
                    affectedAreas = listOf("Toàn tuyến"),
                    recommendations = listOf("Mang theo ô", "Đi chậm và cẩn thận")
                )
            )
        }

        // Check for extreme temperatures
        val avgTemp = (departure.temperature + arrival.temperature) / 2
        if (avgTemp > 35.0) {
            alerts.add(
                WeatherAlert(
                    type = WeatherAlertType.EXTREME_HEAT,
                    severity = WeatherWarningSeverity.HIGH,
                    title = "Nắng nóng",
                    message = "Nhiệt độ cao ${avgTemp.toInt()}°C",
                    startTime = departure.time,
                    endTime = arrival.time,
                    affectedAreas = listOf("Toàn tuyến"),
                    recommendations = listOf("Mang nước", "Tránh tiếp xúc trực tiếp với nắng")
                )
            )
        }

        return alerts
    }

    private fun determineOverallSeverity(
        departureWarning: TripWeatherWarning?,
        returnWarning: TripWeatherWarning?
    ): WeatherWarningSeverity {
        val departureSeverity = departureWarning?.severity ?: WeatherWarningSeverity.NONE
        val returnSeverity = returnWarning?.severity ?: WeatherWarningSeverity.NONE

        return maxOf(departureSeverity, returnSeverity)
    }

    private fun generateContextualRecommendations(
        departureWarning: TripWeatherWarning?,
        returnWarning: TripWeatherWarning?,
        overallSeverity: WeatherWarningSeverity
    ): Pair<String, List<String>> {
        val recommendations = mutableListOf<String>()

        val primaryMessage = when (overallSeverity) {
            WeatherWarningSeverity.NONE -> "Thời tiết thuận lợi cho cả ngày"
            WeatherWarningSeverity.LOW -> "Thời tiết tương đối ổn định"
            WeatherWarningSeverity.MODERATE -> "Cần chú ý thời tiết trong ngày"
            WeatherWarningSeverity.HIGH -> "Thời tiết có thể ảnh hưởng đến di chuyển"
            WeatherWarningSeverity.CRITICAL -> "Thời tiết nguy hiểm, cân nhắc hoãn di chuyển"
        }

        // Collect recommendations from both trips
        departureWarning?.recommendations?.let { recommendations.addAll(it) }
        returnWarning?.recommendations?.let { recommendations.addAll(it) }

        // Remove duplicates and limit to top 5
        val uniqueRecommendations = recommendations.distinct().take(5)

        return Pair(primaryMessage, uniqueRecommendations)
    }

    private fun generateTripRecommendations(
        alerts: List<WeatherAlert>,
        tripType: TripType
    ): List<String> {
        val recommendations = mutableListOf<String>()

        alerts.forEach { alert ->
            recommendations.addAll(alert.recommendations)
        }

        // Add trip-specific recommendations
        when (tripType) {
            TripType.TO_WORK -> {
                if (alerts.any { it.type == WeatherAlertType.HEAVY_RAIN }) {
                    recommendations.add("Khởi hành sớm hơn 15-20 phút")
                }
            }
            TripType.TO_HOME -> {
                if (alerts.any { it.type == WeatherAlertType.EXTREME_HEAT }) {
                    recommendations.add("Chờ đến chiều mát hơn nếu có thể")
                }
            }
        }

        return recommendations.distinct()
    }

    private fun determineConditionSeverity(description: String): Int {
        return when {
            description.contains("heavy", ignoreCase = true) -> 4
            description.contains("moderate", ignoreCase = true) -> 3
            description.contains("light", ignoreCase = true) -> 2
            else -> 1
        }
    }

    private fun determinePrecipitationType(description: String): PrecipitationType {
        return when {
            description.contains("rain", ignoreCase = true) -> PrecipitationType.RAIN
            description.contains("snow", ignoreCase = true) -> PrecipitationType.SNOW
            description.contains("sleet", ignoreCase = true) -> PrecipitationType.SLEET
            description.contains("hail", ignoreCase = true) -> PrecipitationType.HAIL
            else -> PrecipitationType.NONE
        }
    }
}

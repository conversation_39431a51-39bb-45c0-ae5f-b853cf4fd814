package com.workly.app.data.repository

import com.workly.app.data.database.dao.AttendanceDao
import com.workly.app.data.model.AttendanceLog
import com.workly.app.data.model.AttendanceType
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate
import java.time.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AttendanceRepositoryImpl @Inject constructor(
    private val attendanceDao: AttendanceDao
) : AttendanceRepository {

    override fun getAllAttendanceLogs(): Flow<List<AttendanceLog>> {
        return attendanceDao.getAllAttendanceLogs()
    }

    override suspend fun getAttendanceLogById(id: String): AttendanceLog? {
        return attendanceDao.getAttendanceLogById(id)
    }

    override suspend fun insertAttendanceLog(log: AttendanceLog) {
        attendanceDao.insertAttendanceLog(log)
    }

    override suspend fun insertAttendanceLogs(logs: List<AttendanceLog>) {
        attendanceDao.insertAttendanceLogs(logs)
    }

    override suspend fun updateAttendanceLog(log: AttendanceLog) {
        attendanceDao.updateAttendanceLog(log)
    }

    override suspend fun deleteAttendanceLog(log: AttendanceLog) {
        attendanceDao.deleteAttendanceLog(log)
    }

    override suspend fun deleteAttendanceLogById(id: String) {
        attendanceDao.deleteAttendanceLogById(id)
    }

    override suspend fun deleteAllAttendanceLogs() {
        attendanceDao.deleteAllAttendanceLogs()
    }

    override suspend fun deleteLogsForDate(date: LocalDate) {
        attendanceDao.deleteLogsForDate(date)
    }

    override suspend fun getLogsForDate(date: LocalDate): List<AttendanceLog> {
        return attendanceDao.getLogsForDate(date)
    }

    override fun getLogsForDateFlow(date: LocalDate): Flow<List<AttendanceLog>> {
        return attendanceDao.getLogsForDateFlow(date)
    }

    override suspend fun getLogsForDateRange(startDate: LocalDate, endDate: LocalDate): List<AttendanceLog> {
        return attendanceDao.getLogsForDateRange(startDate, endDate)
    }

    override fun getLogsForDateRangeFlow(startDate: LocalDate, endDate: LocalDate): Flow<List<AttendanceLog>> {
        return attendanceDao.getLogsForDateRangeFlow(startDate, endDate)
    }

    override suspend fun getLogsByShiftId(shiftId: String): List<AttendanceLog> {
        return attendanceDao.getLogsByShiftId(shiftId)
    }

    override fun getLogsByShiftIdFlow(shiftId: String): Flow<List<AttendanceLog>> {
        return attendanceDao.getLogsByShiftIdFlow(shiftId)
    }

    override suspend fun getLogsByType(type: AttendanceType): List<AttendanceLog> {
        return attendanceDao.getLogsByType(type)
    }

    override suspend fun getRecentLogs(fromDate: LocalDateTime, limit: Int): List<AttendanceLog> {
        return attendanceDao.getRecentLogs(fromDate, limit)
    }

    override fun getRecentLogsFlow(fromDate: LocalDateTime, limit: Int): Flow<List<AttendanceLog>> {
        return attendanceDao.getRecentLogsFlow(fromDate, limit)
    }

    override suspend fun getAutoGeneratedLogs(): List<AttendanceLog> {
        return attendanceDao.getAutoGeneratedLogs()
    }

    override suspend fun getManualLogs(): List<AttendanceLog> {
        return attendanceDao.getManualLogs()
    }

    override suspend fun getLogsCount(): Int {
        return attendanceDao.getLogsCount()
    }

    override suspend fun getLogsCountForDate(date: LocalDate): Int {
        return attendanceDao.getLogsCountForDate(date)
    }
}

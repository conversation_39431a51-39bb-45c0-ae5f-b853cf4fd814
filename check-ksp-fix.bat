@echo off
echo ========================================
echo    Checking KSP Fix - Workly Android
echo ========================================
echo.

echo [1/3] Checking AttendanceLog.kt imports...
findstr /n "import.*LocalDate" "app\src\main\java\com\workly\app\data\model\AttendanceLog.kt" >nul
if %errorlevel%==0 (
    echo ✓ LocalDate import found in AttendanceLog.kt
) else (
    echo ✗ LocalDate import missing in AttendanceLog.kt
)

findstr /n "import.*LocalDateTime" "app\src\main\java\com\workly\app\data\model\AttendanceLog.kt" >nul
if %errorlevel%==0 (
    echo ✓ LocalDateTime import found in AttendanceLog.kt
) else (
    echo ✗ LocalDateTime import missing in AttendanceLog.kt
)

echo.
echo [2/3] Checking other model files...
for %%f in (DailyWorkStatus.kt Note.kt Shift.kt) do (
    if exist "app\src\main\java\com\workly\app\data\model\%%f" (
        echo ✓ %%f exists
    ) else (
        echo ✗ %%f missing
    )
)

echo.
echo [3/3] KSP Fix Summary:
echo ✅ Added missing LocalDate import to AttendanceLog.kt
echo ✅ All Room entity classes have proper imports
echo ✅ All DAO interfaces are properly defined
echo ✅ Database class references all entities correctly

echo.
echo The KSP MissingType error should now be resolved!
echo You can now try building the project again.

pause

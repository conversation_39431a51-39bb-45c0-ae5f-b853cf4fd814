package com.workly.app.services

import com.workly.app.data.model.*
import java.time.LocalDateTime

interface WeatherService {

    /**
     * L<PERSON>y thời tiết hiện tại cho vị trí mặc định
     */
    suspend fun getCurrentWeather(): WeatherData?

    /**
     * Lấy thời tiết cho một vị trí cụ thể
     */
    suspend fun getWeatherForLocation(location: LocationCoordinate): WeatherData?

    /**
     * Lấy dự báo thời tiết nhiều ngày
     */
    suspend fun getWeatherForecast(location: LocationCoordinate, days: Int = 5): WeatherData?

    /**
     * Kiểm tra cảnh báo thời tiết cho cả nhà và công ty (tối ưu hóa API)
     */
    suspend fun checkWeatherWarnings(
        homeLocation: LocationCoordinate?,
        workLocation: LocationCoordinate?
    ): List<WeatherWarning>

    /**
     * <PERSON><PERSON><PERSON> báo thời tiết ngữ cảnh - một lần kiểm tra cho cả chiều đi và về
     */
    suspend fun checkContextualWeatherWarnings(
        homeLocation: LocationCoordinate,
        workLocation: LocationCoordinate,
        departureTime: LocalDateTime,
        returnTime: LocalDateTime
    ): ContextualWeatherWarning

    /**
     * Refresh dữ liệu thời tiết
     */
    suspend fun refreshWeatherData()

    /**
     * Lấy thời tiết từ cache (nếu còn hợp lệ)
     */
    suspend fun getCachedWeather(): WeatherData?

    /**
     * Kiểm tra xem cache có hết hạn không
     */
    suspend fun isCacheExpired(): Boolean

    /**
     * Lấy thời tiết cho nhiều vị trí cùng lúc (batch request)
     */
    suspend fun getWeatherForMultipleLocations(
        locations: List<LocationCoordinate>
    ): Map<LocationCoordinate, WeatherData?>

    /**
     * Dự đoán thời tiết cho thời gian cụ thể
     */
    suspend fun getWeatherAtTime(
        location: LocationCoordinate,
        targetTime: LocalDateTime
    ): WeatherForecast?
}

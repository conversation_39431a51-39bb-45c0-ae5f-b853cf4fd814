package com.workly.app.services

import com.workly.app.data.model.*
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Service xử lý logic nút đa năng động và các hành động chấm công
 */
interface AttendanceActionService {
    
    /**
     * <PERSON><PERSON><PERSON> định trạng thái hiện tại của nút đa năng
     */
    suspend fun getCurrentButtonState(): AttendanceButtonState
    
    /**
     * Lấy text hiển thị cho nút
     */
    suspend fun getButtonText(state: AttendanceButtonState): String
    
    /**
     * Lấy icon cho nút
     */
    suspend fun getButtonIcon(state: AttendanceButtonState): String
    
    /**
     * Lấy màu cho nút
     */
    suspend fun getButtonColor(state: AttendanceButtonState): Int
    
    /**
     * Kiểm tra xem nút có nên hiển thị không (trong cửa sổ hoạt động)
     */
    suspend fun shouldShowButton(): Boolean
    
    /**
     * Xử lý khi người dùng bấm nút đa năng
     */
    suspend fun handleButtonPress(): AttendanceActionResult
    
    /**
     * Lấy thông tin trạng thái hiện tại để hiển thị
     */
    suspend fun getCurrentStatusInfo(): AttendanceStatusInfo
    
    /**
     * Kiểm tra có trong "cửa sổ hoạt động" không
     * (từ 1h trước departureTime đến 2h sau endTime)
     */
    suspend fun isInActiveWindow(): Boolean
    
    /**
     * Lấy thời gian còn lại đến hành động tiếp theo
     */
    suspend fun getTimeToNextAction(): TimeToNextAction?
    
    /**
     * Xử lý logic phát hiện "bấm nhanh"
     */
    suspend fun handleRapidPress(): RapidPressResult
}

/**
 * Trạng thái nút đa năng
 */
enum class AttendanceButtonState {
    HIDDEN,           // Ẩn (ngoài cửa sổ hoạt động)
    GO_WORK,          // Đi làm
    CHECK_IN,         // Vào làm
    PUNCH,            // Chấm công (nếu có)
    CHECK_OUT,        // Ra về
    COMPLETED,        // Đã hoàn thành
    WAITING,          // Chờ (giữa các hành động)
    ERROR             // Lỗi
}

/**
 * Kết quả hành động chấm công
 */
data class AttendanceActionResult(
    val success: Boolean,
    val action: AttendanceType,
    val timestamp: LocalDateTime,
    val message: String,
    val nextState: AttendanceButtonState,
    val nextActionTime: LocalDateTime? = null,
    val errors: List<String> = emptyList()
)

/**
 * Thông tin trạng thái hiện tại
 */
data class AttendanceStatusInfo(
    val currentState: AttendanceButtonState,
    val statusText: String,
    val detailText: String? = null,
    val progressPercentage: Int = 0, // 0-100
    val isInActiveWindow: Boolean,
    val activeShift: Shift?,
    val todayWorkDate: LocalDate,
    val lastAction: AttendanceLog? = null,
    val nextExpectedAction: AttendanceType? = null,
    val nextExpectedTime: LocalDateTime? = null
)

/**
 * Thời gian đến hành động tiếp theo
 */
data class TimeToNextAction(
    val action: AttendanceType,
    val timeRemaining: Long, // milliseconds
    val formattedTime: String,
    val isOverdue: Boolean = false
)

/**
 * Kết quả xử lý bấm nhanh
 */
data class RapidPressResult(
    val isRapidPress: Boolean,
    val timeSinceLastPress: Long, // milliseconds
    val shouldIgnore: Boolean,
    val warningMessage: String? = null
)

/**
 * Cửa sổ hoạt động của ca làm việc
 */
data class ActiveWindow(
    val startTime: LocalDateTime, // 1h trước departureTime
    val endTime: LocalDateTime,   // 2h sau endTime
    val shift: Shift,
    val workDate: LocalDate
)

/**
 * Context cho việc xác định trạng thái nút
 */
data class ButtonStateContext(
    val currentTime: LocalDateTime,
    val activeShift: Shift?,
    val workDate: LocalDate,
    val todayLogs: List<AttendanceLog>,
    val dailyStatus: DailyWorkStatus?,
    val userSettings: UserSettings,
    val isInActiveWindow: Boolean,
    val lastActionTime: LocalDateTime? = null
)

/**
 * Enum cho các giai đoạn trong ngày làm việc
 */
enum class WorkDayPhase {
    BEFORE_DEPARTURE,    // Trước giờ đi làm
    DEPARTURE_TIME,      // Giờ đi làm
    TRAVEL_TO_WORK,      // Đang đi làm
    CHECK_IN_TIME,       // Giờ vào làm
    WORKING,             // Đang làm việc
    CHECK_OUT_TIME,      // Giờ ra về
    TRAVEL_HOME,         // Đang về nhà
    COMPLETED,           // Đã hoàn thành
    AFTER_HOURS          // Sau giờ làm việc
}

/**
 * Cấu hình hành vi nút
 */
data class ButtonBehaviorConfig(
    val enableRapidPressDetection: Boolean = true,
    val rapidPressThresholdSeconds: Int = 30,
    val enableLocationVerification: Boolean = false,
    val requireConfirmation: Boolean = false,
    val enableHapticFeedback: Boolean = true,
    val enableSoundFeedback: Boolean = true,
    val autoAdvanceToNext: Boolean = true,
    val showCountdown: Boolean = true
)

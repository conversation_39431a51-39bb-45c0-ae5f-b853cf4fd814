@echo off
echo ========================================
echo    Final Hilt Fix Check - Workly Android
echo ========================================
echo.

echo [1/5] Checking Hilt Work dependency...
findstr /n "androidx.hilt:hilt-work" "app\build.gradle.kts" >nul
if %errorlevel%==0 (
    echo ✓ Hilt Work dependency found
) else (
    echo ✗ Hilt Work dependency missing
)

findstr /n "androidx.hilt:hilt-compiler" "app\build.gradle.kts" >nul
if %errorlevel%==0 (
    echo ✓ Hilt compiler dependency found
) else (
    echo ✗ Hilt compiler dependency missing
)

echo.
echo [2/5] Checking Worker annotations...
findstr /n "@HiltWorker" "app\src\main\java\com\workly\app\workers\ShiftRotationWorker.kt" >nul
if %errorlevel%==0 (
    echo ✓ ShiftRotationWorker has @HiltWorker annotation
) else (
    echo ✗ ShiftRotationWorker missing @HiltWorker annotation
)

findstr /n "@HiltWorker" "app\src\main\java\com\workly\app\workers\WeatherWarningWorker.kt" >nul
if %errorlevel%==0 (
    echo ✓ WeatherWarningWorker has @HiltWorker annotation
) else (
    echo ✗ WeatherWarningWorker missing @HiltWorker annotation
)

echo.
echo [3/5] Checking Application class...
findstr /n "@HiltAndroidApp" "app\src\main\java\com\workly\app\WorklyApplication.kt" >nul
if %errorlevel%==0 (
    echo ✓ WorklyApplication has @HiltAndroidApp annotation
) else (
    echo ✗ WorklyApplication missing @HiltAndroidApp annotation
)

findstr /n "HiltWorkerFactory" "app\src\main\java\com\workly\app\WorklyApplication.kt" >nul
if %errorlevel%==0 (
    echo ✓ HiltWorkerFactory injection found
) else (
    echo ✗ HiltWorkerFactory injection missing
)

echo.
echo [4/5] Checking all service bindings...
set /a count=0
for %%s in (bindShiftRepository bindAttendanceRepository bindNoteRepository bindSettingsRepository bindDailyWorkStatusRepository bindLocationService bindNotificationService bindWeatherService bindReminderSchedulingService bindShiftRotationService bindWorkCalculationService) do (
    findstr /n "%%s" "app\src\main\java\com\workly\app\di\AppModule.kt" >nul
    if !errorlevel!==0 (
        set /a count+=1
    )
)
echo ✓ Found %count% service bindings in AppModule.kt

echo.
echo [5/5] Final Hilt Fix Summary:
echo ✅ Added androidx.hilt:hilt-work:1.1.0 dependency
echo ✅ Added androidx.hilt:hilt-compiler:1.1.0 for KSP
echo ✅ All Workers have proper @HiltWorker annotations
echo ✅ All service implementations have proper @Inject constructors
echo ✅ All service interfaces are bound in AppModule.kt
echo ✅ WorklyApplication properly configured for Hilt + WorkManager

echo.
echo The error.NonExistentClass issue should now be resolved!
echo This was caused by missing Hilt Work Manager integration.

pause

package com.workly.app.ui.home

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.workly.app.data.model.WeeklyDisplayItem
import com.workly.app.data.model.WeeklyDisplayStatus
import com.workly.app.ui.components.UpdateStatusDialog
import com.workly.app.ui.components.WeeklyStatusGrid

/**
 * Ví dụ về cách sử dụng WeeklyStatusGrid trong HomeFragment
 * Đ<PERSON><PERSON> là một composable mẫu để tham khảo
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreenExample(
    viewModel: HomeViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    var showUpdateDialog by remember { mutableStateOf(false) }
    var selectedItem by remember { mutableStateOf<WeeklyDisplayItem?>(null) }

    // Hiển thị dialog cập nhật trạng thái
    selectedItem?.let { item ->
        if (showUpdateDialog) {
            UpdateStatusDialog(
                item = item,
                onDismiss = { 
                    showUpdateDialog = false
                    selectedItem = null
                },
                onStatusUpdate = { newStatus ->
                    viewModel.updateWeeklyStatusItem(item, newStatus)
                    showUpdateDialog = false
                    selectedItem = null
                }
            )
        }
    }

    // Hiển thị error message nếu có
    uiState.errorMessage?.let { message ->
        LaunchedEffect(message) {
            // Có thể hiển thị Snackbar hoặc Toast
            // Sau đó clear error
            viewModel.clearError()
        }
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Header
            Text(
                text = "Workly - Trang chủ",
                style = MaterialTheme.typography.headlineMedium
            )
        }

        item {
            // Weather Widget (placeholder)
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Thời tiết",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = "25°C - Nắng",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        item {
            // Multi-function Button (placeholder)
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Button(
                        onClick = { /* Handle button click */ },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(text = uiState.buttonText.ifEmpty { "Chấm công" })
                    }
                    
                    if (uiState.buttonStatusText?.isNotEmpty() == true) {
                        Text(
                            text = uiState.buttonStatusText,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            }
        }

        item {
            // Weekly Status Grid - Đây là phần chính
            WeeklyStatusGrid(
                weeklyItems = uiState.weeklyStatus,
                onItemClick = { item ->
                    viewModel.onWeeklyStatusItemClick(item)
                },
                onItemLongClick = { item ->
                    if (item.isFuture) {
                        selectedItem = item
                        showUpdateDialog = true
                    }
                    viewModel.onWeeklyStatusItemLongClick(item)
                },
                modifier = Modifier.fillMaxWidth()
            )
        }

        item {
            // Week Range Display
            if (uiState.currentWeekRange.isNotEmpty()) {
                Text(
                    text = "Tuần: ${uiState.currentWeekRange}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        item {
            // Quick Stats (placeholder)
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Thống kê nhanh",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Column {
                            Text(
                                text = "Giờ tuần này",
                                style = MaterialTheme.typography.bodySmall
                            )
                            Text(
                                text = "${uiState.thisWeekHours}h",
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                        Column {
                            Text(
                                text = "Điểm tuân thủ",
                                style = MaterialTheme.typography.bodySmall
                            )
                            Text(
                                text = "${(uiState.complianceScore * 100).toInt()}%",
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    }
                }
            }
        }

        item {
            // Notes Section (placeholder)
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Ghi chú",
                        style = MaterialTheme.typography.titleMedium
                    )
                    
                    if (uiState.displayedNotes.isEmpty()) {
                        Text(
                            text = "Không có ghi chú nào",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    } else {
                        uiState.displayedNotes.forEach { note ->
                            Text(
                                text = "• ${note.title}",
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.padding(vertical = 2.dp)
                            )
                        }
                        
                        if (uiState.hasMoreNotes) {
                            TextButton(
                                onClick = { /* Navigate to notes */ }
                            ) {
                                Text("Xem thêm...")
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * Preview cho WeeklyStatusGrid
 */
@Composable
fun WeeklyStatusGridPreview() {
    MaterialTheme {
        Surface {
            // Tạo dữ liệu mẫu
            val sampleItems = listOf(
                WeeklyDisplayItem(
                    date = java.time.LocalDate.now().minusDays(6),
                    dayOfWeek = "T2",
                    dayNumber = "01",
                    status = WeeklyDisplayStatus.DU_CONG
                ),
                WeeklyDisplayItem(
                    date = java.time.LocalDate.now().minusDays(5),
                    dayOfWeek = "T3", 
                    dayNumber = "02",
                    status = WeeklyDisplayStatus.THIEU_LOG
                ),
                WeeklyDisplayItem(
                    date = java.time.LocalDate.now().minusDays(4),
                    dayOfWeek = "T4",
                    dayNumber = "03", 
                    status = WeeklyDisplayStatus.DI_MUON
                ),
                WeeklyDisplayItem(
                    date = java.time.LocalDate.now().minusDays(3),
                    dayOfWeek = "T5",
                    dayNumber = "04",
                    status = WeeklyDisplayStatus.NGHI_PHEP
                ),
                WeeklyDisplayItem(
                    date = java.time.LocalDate.now().minusDays(2),
                    dayOfWeek = "T6",
                    dayNumber = "05",
                    status = WeeklyDisplayStatus.NGHI_BENH
                ),
                WeeklyDisplayItem(
                    date = java.time.LocalDate.now().minusDays(1),
                    dayOfWeek = "T7",
                    dayNumber = "06",
                    status = WeeklyDisplayStatus.DANG_LAM,
                    isToday = true
                ),
                WeeklyDisplayItem(
                    date = java.time.LocalDate.now(),
                    dayOfWeek = "CN",
                    dayNumber = "07",
                    status = WeeklyDisplayStatus.CHUA_CAP_NHAT,
                    isFuture = true
                )
            )
            
            WeeklyStatusGrid(
                weeklyItems = sampleItems,
                onItemClick = { },
                onItemLongClick = { },
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

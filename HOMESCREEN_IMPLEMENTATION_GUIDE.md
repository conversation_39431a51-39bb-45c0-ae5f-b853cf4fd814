# HomeScreen Implementation Guide

## Tổng quan

Màn hình <PERSON> (HomeScreen) của Workly được thiết kế theo đặc tả chi tiết với đầy đủ các thành phần và tương tác người dùng.

## Cấu trúc Layout

```
┌─────────────────────────────────────┐
│ Top Bar (Ngày/giờ + Settings)      │
├─────────────────────────────────────┤
│ Weather Warning Banner (nếu có)    │
├─────────────────────────────────────┤
│ Active Shift Display               │
├─────────────────────────────────────┤
│ Multi-Function Button              │
│ ├─ Main Button (120dp circle)      │
│ ├─ Reset Button (góc trên phải)    │
│ └─ Punch Button (dưới main)        │
├─────────────────────────────────────┤
│ Button Action History              │
├─────────────────────────────────────┤
│ Weekly Status Grid (7 ô T2-CN)     │
├─────────────────────────────────────┤
│ Work Notes Section (tối đa 3)      │
├─────────────────────────────────────┤
│ Quick Stats (optional)             │
└─────────────────────────────────────┘
```

## Components Chính

### 1. Multi-Function Button

**File**: `MultiFunctionButton.kt`, `MultiFunctionButtonState.kt`

**Chế độ hoạt động**:
- **Full Mode**: Đ<PERSON><PERSON> đủ các trạng thái
- **Simple Mode**: <PERSON><PERSON><PERSON> gọ<PERSON> (cấu hình trong UserSettings)

**Luồng trạng thái Full**:
```
Đi Làm 🚶‍♂️ → Đang đi ⏳ → Chấm Công Vào 🏢 → 
Đang làm việc ⏳ → Chấm Công Ra 🚪 → 
Sẵn sàng hoàn tất 🏁 → Hoàn Tất ✅ → Đã Hoàn Tất ✅
```

**Tính năng**:
- Text và icon thay đổi theo trạng thái
- Progress indicator cho trạng thái chờ
- Nút Reset nhỏ (🔄) xuất hiện khi có log
- Nút Ký Công xuất hiện sau CHECK_IN

### 2. Weather Warning Banner

**File**: `WeatherWarningBanner.kt`

**Tính năng**:
- Hiển thị cảnh báo thời tiết theo loại
- Màu sắc và icon phù hợp
- Nút "Đã biết" để dismiss
- Animation slide in/out

### 3. Weekly Status Grid

**File**: `WeeklyStatusGrid.kt`, `WeeklyStatusDisplay.kt`

**Tính năng**:
- 7 ô từ T2-CN với ngày, thứ, icon
- Tương tác click/long press
- Bottom sheet chi tiết
- Dialog cập nhật trạng thái tương lai

### 4. Work Notes Section

**File**: `WorkNotesSection.kt`

**Tính năng**:
- Hiển thị tối đa 3 ghi chú
- Sắp xếp theo reminderTime/updatedAt
- Nút Edit ✏️ và Delete 🗑️
- Nút "Thêm Ghi Chú" +

## Tương tác Người dùng

### Multi-Function Button
- **Click**: Thực hiện hành động chấm công
- **Reset Button**: Xác nhận → Xóa log ngày hiện tại
- **Punch Button**: Chấm công giữa ca

### Weekly Status Grid
- **Click**: Mở Bottom Sheet chi tiết
- **Long Press**: Cập nhật trạng thái (ngày tương lai)

### Work Notes
- **Edit**: Navigate to edit screen
- **Delete**: Confirmation dialog
- **Add**: Navigate to add screen

## Data Flow

### ViewModel → UI State
```kotlin
HomeUiState(
    buttonInfo: MultiFunctionButtonInfo?,
    buttonActionHistory: List<ButtonActionHistory>,
    weeklyStatus: List<WeeklyDisplayItem>,
    weatherWarning: WeatherWarning?,
    displayedNotes: List<Note>,
    // ...
)
```

### Button State Management
```kotlin
// Load current state from logs
loadButtonState() → determineButtonStateFromLogs()

// Handle button click
handleButtonClick() → createAttendanceLog() → updateButtonState()

// Reset functionality
resetDayStatus() → deleteLogsForDate() → resetToDefaultState()
```

## Cấu hình

### UserSettings
```kotlin
data class UserSettings(
    enableSimpleMode: Boolean = false,  // Button mode
    showWeatherWidget: Boolean = true,
    showWeeklyGrid: Boolean = true,
    showNotesSection: Boolean = true,
    // ...
)
```

### Shift Settings
```kotlin
data class Shift(
    showPunch: Boolean = false,  // Hiển thị nút ký công
    // ...
)
```

## Error Handling

### UI State Errors
```kotlin
data class HomeUiState(
    errorMessage: String? = null,
    hasError: Boolean = false
)
```

### Error Display
- Snackbar cho lỗi tạm thời
- Dialog cho lỗi nghiêm trọng
- Auto-clear errors sau khi hiển thị

## Performance Optimizations

### LazyColumn
- Sử dụng cho main content
- Proper key management
- Efficient recomposition

### State Management
- `collectAsStateWithLifecycle()` để tránh memory leaks
- Minimal state updates
- Efficient data loading

## Testing

### Unit Tests
- ViewModel logic
- State transformations
- Button state flow

### UI Tests
- Component interactions
- Navigation flows
- Error scenarios

## Customization

### Themes
- Material 3 design system
- Dynamic colors support
- Dark/Light mode

### Accessibility
- Content descriptions
- Semantic properties
- Screen reader support

## Migration Notes

### From Old HomeFragment
1. Replace Fragment with Composable
2. Update ViewModel dependencies
3. Migrate state management
4. Update navigation

### Breaking Changes
- `AttendanceButtonState` → `MultiFunctionButtonState`
- `WeeklyStatusItem` → `WeeklyDisplayItem`
- New button interaction patterns

## Future Enhancements

### Planned Features
- Voice commands for button
- Gesture shortcuts
- Widget support
- Offline sync indicators

### Extensibility
- Plugin architecture for custom buttons
- Configurable layouts
- Custom note types
- Advanced weather integration

## Files Created

```
app/src/main/java/com/workly/app/
├── data/model/
│   ├── MultiFunctionButtonState.kt
│   └── WeeklyStatusDisplay.kt
├── ui/components/
│   ├── MultiFunctionButton.kt
│   ├── WeatherWarningBanner.kt
│   ├── WorkNotesSection.kt
│   └── WeeklyStatusGrid.kt (updated)
├── ui/home/
│   ├── HomeScreen.kt
│   ├── HomeUiState.kt (updated)
│   └── HomeViewModel.kt (updated)
└── docs/
    ├── HOMESCREEN_IMPLEMENTATION_GUIDE.md
    └── WEEKLY_STATUS_GRID_GUIDE.md
```

## Quick Start

1. **Import components** vào project
2. **Update dependencies** trong ViewModel
3. **Configure navigation** cho các screens
4. **Test interactions** với sample data
5. **Customize themes** theo brand

Tất cả components đã được thiết kế modular và có thể tái sử dụng trong các phần khác của ứng dụng.

package com.workly.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.time.LocalDateTime

/**
 * Cảnh báo thời tiết ngữ cảnh - tối ưu hóa cho một lần kiểm tra
 */
@Parcelize
data class ContextualWeatherWarning(
    // Thông tin chung
    val checkTime: LocalDateTime,
    val isValid: Boolean,
    
    // Cảnh báo cho chiều đi (nhà -> công ty)
    val departureWarning: TripWeatherWarning?,
    
    // Cảnh báo cho chiều về (công ty -> nhà)  
    val returnWarning: TripWeatherWarning?,
    
    // Cảnh báo tổng hợp
    val overallSeverity: WeatherWarningSeverity,
    val primaryMessage: String,
    val actionRecommendations: List<String>,
    
    // Metadata
    val dataSource: String = "OpenWeatherMap",
    val cacheExpiresAt: LocalDateTime
) : Parcelable

/**
 * <PERSON><PERSON>nh báo thời tiết cho một chuyến đi
 */
@Parcelize
data class TripWeatherWarning(
    val tripType: TripType,
    val fromLocation: LocationCoordinate,
    val toLocation: LocationCoordinate,
    val departureTime: LocalDateTime,
    val estimatedArrivalTime: LocalDateTime,
    
    // Thời tiết tại điểm xuất phát
    val departureWeather: WeatherSnapshot,
    
    // Thời tiết tại điểm đến
    val arrivalWeather: WeatherSnapshot,
    
    // Thời tiết trong suốt chuyến đi
    val tripWeather: List<WeatherSnapshot>,
    
    // Cảnh báo và khuyến nghị
    val warnings: List<WeatherAlert>,
    val severity: WeatherWarningSeverity,
    val recommendations: List<String>
) : Parcelable

/**
 * Loại chuyến đi
 */
enum class TripType {
    TO_WORK,    // Đi làm
    TO_HOME     // Về nhà
}

/**
 * Snapshot thời tiết tại một thời điểm
 */
@Parcelize
data class WeatherSnapshot(
    val time: LocalDateTime,
    val location: LocationCoordinate,
    val temperature: Double,
    val humidity: Int,
    val windSpeed: Double,
    val windDirection: Int,
    val visibility: Double,
    val uvIndex: Double,
    val condition: WeatherCondition,
    val precipitation: PrecipitationInfo,
    val airQuality: AirQualityInfo? = null
) : Parcelable

/**
 * Điều kiện thời tiết
 */
@Parcelize
data class WeatherCondition(
    val main: String,           // "Rain", "Snow", "Clear", etc.
    val description: String,    // "light rain", "heavy snow", etc.
    val icon: String,          // Weather icon code
    val severity: Int          // 1-5 scale
) : Parcelable

/**
 * Thông tin mưa/tuyết
 */
@Parcelize
data class PrecipitationInfo(
    val type: PrecipitationType,
    val intensity: Double,      // mm/h
    val probability: Int,       // 0-100%
    val duration: Int          // minutes
) : Parcelable

enum class PrecipitationType {
    NONE, RAIN, SNOW, SLEET, HAIL
}

/**
 * Thông tin chất lượng không khí
 */
@Parcelize
data class AirQualityInfo(
    val aqi: Int,              // Air Quality Index
    val pm25: Double,          // PM2.5 concentration
    val pm10: Double,          // PM10 concentration
    val co: Double,            // Carbon monoxide
    val no2: Double,           // Nitrogen dioxide
    val so2: Double,           // Sulfur dioxide
    val o3: Double             // Ozone
) : Parcelable

/**
 * Cảnh báo thời tiết cụ thể
 */
@Parcelize
data class WeatherAlert(
    val type: WeatherAlertType,
    val severity: WeatherWarningSeverity,
    val title: String,
    val message: String,
    val startTime: LocalDateTime,
    val endTime: LocalDateTime,
    val affectedAreas: List<String>,
    val recommendations: List<String>
) : Parcelable

/**
 * Loại cảnh báo thời tiết
 */
enum class WeatherAlertType {
    HEAVY_RAIN,         // Mưa to
    THUNDERSTORM,       // Dông bão
    STRONG_WIND,        // Gió mạnh
    EXTREME_HEAT,       // Nắng nóng cực đoan
    EXTREME_COLD,       // Lạnh cực đoan
    SNOW,               // Tuyết
    HAIL,               // Mưa đá
    FOG,                // Sương mù
    DUST_STORM,         // Bão cát
    POOR_AIR_QUALITY,   // Chất lượng không khí kém
    UV_HIGH,            // Tia UV cao
    FLOOD_RISK,         // Nguy cơ lũ lụt
    ICE_FORMATION       // Nguy cơ đóng băng
}

/**
 * Mức độ nghiêm trọng cảnh báo
 */
enum class WeatherWarningSeverity {
    NONE,       // Không có cảnh báo
    LOW,        // Thấp - chú ý
    MODERATE,   // Vừa - chuẩn bị
    HIGH,       // Cao - cảnh giác
    CRITICAL    // Cực kỳ nghiêm trọng - nguy hiểm
}

/**
 * Cấu hình cảnh báo thời tiết
 */
@Parcelize
data class WeatherWarningConfig(
    val enabledAlertTypes: List<WeatherAlertType>,
    val minimumSeverity: WeatherWarningSeverity,
    val checkIntervalMinutes: Int = 60,
    val advanceWarningHours: Int = 2,
    val temperatureThresholds: TemperatureThresholds,
    val precipitationThresholds: PrecipitationThresholds,
    val windSpeedThresholds: WindSpeedThresholds,
    val enableAirQualityWarnings: Boolean = true,
    val enableUVWarnings: Boolean = true
) : Parcelable

@Parcelize
data class TemperatureThresholds(
    val extremeHeat: Double = 35.0,     // °C
    val extremeCold: Double = 5.0,      // °C
    val comfortMin: Double = 18.0,      // °C
    val comfortMax: Double = 28.0       // °C
) : Parcelable

@Parcelize
data class PrecipitationThresholds(
    val lightRain: Double = 2.5,        // mm/h
    val moderateRain: Double = 10.0,    // mm/h
    val heavyRain: Double = 50.0,       // mm/h
    val minProbability: Int = 30        // %
) : Parcelable

@Parcelize
data class WindSpeedThresholds(
    val moderate: Double = 20.0,        // km/h
    val strong: Double = 40.0,          // km/h
    val dangerous: Double = 60.0        // km/h
) : Parcelable

/**
 * Context cho việc tạo cảnh báo thời tiết
 */
data class WeatherWarningContext(
    val userLocation: LocationCoordinate,
    val workLocation: LocationCoordinate,
    val currentTime: LocalDateTime,
    val departureTime: LocalDateTime,
    val returnTime: LocalDateTime,
    val transportMode: TransportMode,
    val userPreferences: WeatherWarningConfig,
    val historicalData: List<WeatherSnapshot> = emptyList()
)

/**
 * Phương tiện di chuyển
 */
enum class TransportMode {
    WALKING,        // Đi bộ
    BICYCLE,        // Xe đạp
    MOTORCYCLE,     // Xe máy
    CAR,            // Ô tô
    PUBLIC_TRANSPORT, // Phương tiện công cộng
    MIXED           // Kết hợp nhiều phương tiện
}

// Extension functions
fun WeatherWarningSeverity.getColor(): Int {
    return when (this) {
        WeatherWarningSeverity.NONE -> 0xFF4CAF50.toInt()      // Green
        WeatherWarningSeverity.LOW -> 0xFFFFEB3B.toInt()       // Yellow
        WeatherWarningSeverity.MODERATE -> 0xFFFF9800.toInt()  // Orange
        WeatherWarningSeverity.HIGH -> 0xFFFF5722.toInt()      // Deep Orange
        WeatherWarningSeverity.CRITICAL -> 0xFFF44336.toInt()  // Red
    }
}

fun WeatherWarningSeverity.getDisplayText(): String {
    return when (this) {
        WeatherWarningSeverity.NONE -> "Bình thường"
        WeatherWarningSeverity.LOW -> "Chú ý"
        WeatherWarningSeverity.MODERATE -> "Cảnh báo"
        WeatherWarningSeverity.HIGH -> "Nguy hiểm"
        WeatherWarningSeverity.CRITICAL -> "Cực kỳ nguy hiểm"
    }
}

fun WeatherAlertType.getIcon(): String {
    return when (this) {
        WeatherAlertType.HEAVY_RAIN -> "🌧️"
        WeatherAlertType.THUNDERSTORM -> "⛈️"
        WeatherAlertType.STRONG_WIND -> "💨"
        WeatherAlertType.EXTREME_HEAT -> "🌡️"
        WeatherAlertType.EXTREME_COLD -> "🥶"
        WeatherAlertType.SNOW -> "❄️"
        WeatherAlertType.HAIL -> "🧊"
        WeatherAlertType.FOG -> "🌫️"
        WeatherAlertType.DUST_STORM -> "🌪️"
        WeatherAlertType.POOR_AIR_QUALITY -> "😷"
        WeatherAlertType.UV_HIGH -> "☀️"
        WeatherAlertType.FLOOD_RISK -> "🌊"
        WeatherAlertType.ICE_FORMATION -> "🧊"
    }
}
